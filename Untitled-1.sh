#!/bin/bash

# ======================================================================
# Script Name: analyze_contract.sh
# Description: 使用 Mythril 分析 Solidity 智能合约
# Usage: ./analyze_contract.sh /path/to/your/contract.sol
# ======================================================================

# 设置默认参数
MYTHRIEL_IMAGE="mythril/myth:0.24.8"           # Mythril Docker 镜像版本
CONTAINER_NAME="mythril_analysis"             # 容器名称
SOLC_VERSION="--solv 0.4.25"                  # Solidity 编译器版本
TIMEOUT="--execution-timeout 60"              # 执行超时时间（秒）
OUTPUT_FORMAT="-o text"                       # 输出格式
THREADS="-t 3"                                # 线程数
MAPPING_DIR="/contract"                       # 映射到容器内的固定目录

# 检查是否提供了合约路径
if [ $# -ne 1 ]; then
    echo "用法: $0 /path/to/your/contract.sol"
    exit 1
fi

CONTRACT_PATH="$1"
CONTRACT_NAME=$(basename "$CONTRACT_PATH")
CONTAINER_CONTRACT_PATH="$MAPPING_DIR/$CONTRACT_NAME"

# 检查合约文件是否存在
if [ ! -f "$CONTRACT_PATH" ]; then
    echo "错误: 文件 '$CONTRACT_PATH' 不存在。"
    exit 1
fi

# 检查合约pragma版本
PRAGMA_VERSION=$(grep -Eo 'pragma solidity [^;]+' "$CONTRACT_PATH" | awk '{print $3}')
if [[ "$PRAGMA_VERSION" != ^0.4 ]]; then
    echo "警告: 合约pragma版本为 $PRAGMA_VERSION，建议使用 0.4.25 以匹配检测脚本设置。"
fi

# 运行 Mythril 分析并保存输出
ANALYSIS_RESULT="result.txt"
docker run --rm \
    --name "$CONTAINER_NAME" \
    -v "$(pwd)/$CONTRACT_PATH:$CONTAINER_CONTRACT_PATH" \
    "$MYTHRIEL_IMAGE" \
    analyze "$CONTAINER_CONTRACT_PATH" \
    $SOLC_VERSION \
    $TIMEOUT \
    $OUTPUT_FORMAT \
    $THREADS > "$ANALYSIS_RESULT" 2>&1

# 检查命令是否成功执行
if [ $? -eq 0 ]; then
    echo "分析完成。输出结果如下："
    # 高亮显示常见漏洞关键字
    egrep --color=always -i 'reentrancy|overflow|underflow|call.value|delegatecall|tx.origin|assert|suicide|selfdestruct|arbitrary|unprotected' "$ANALYSIS_RESULT" || cat "$ANALYSIS_RESULT"
    echo "\n完整结果已保存到: $(pwd)/$ANALYSIS_RESULT"
else
    echo "分析过程中出现错误。"
    cat "$ANALYSIS_RESULT"
    exit 1
fi
