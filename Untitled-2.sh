#!/bin/bash

MYTHRIL_IMAGE="mythril/myth:0.24.8"
SOLC_VERSION="--solv 0.4.25"
TIMEOUT="--execution-timeout 60"
OUTPUT_FORMAT="-o json"
THREADS="-t 3"
WORKDIR="/home/<USER>/mythril/shell"
CONTAINER_DIR="/contract"

if [ $# -ne 1 ]; then
	    echo "用法: $0 contract.sol"
	        exit 1
fi

CONTRACT_NAME="$1"
CONTRACT_PATH="$WORKDIR/$CONTRACT_NAME"
CONTAINER_CONTRACT_PATH="$CONTAINER_DIR/$CONTRACT_NAME"
TMP_JSON="/tmp/mythril_${CONTRACT_NAME%.*}_result.json"

if [ ! -f "$CONTRACT_PATH" ]; then
	    echo "错误: 文件 '$CONTRACT_PATH' 不存在。"
	        exit 1
fi

docker run --rm \
	    -v "$WORKDIR:$CONTAINER_DIR" \
	        "$MYTHRIL_IMAGE" \
		    analyze "$CONTAINER_CONTRACT_PATH" \
		        $SOLC_VERSION \
			    $TIMEOUT \
			        $OUTPUT_FORMAT \
				    $THREADS > "$TMP_JSON"

echo
echo "====================================================="
echo "              智能合约安全检测官方报告              "
echo "====================================================="
echo "报告生成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo "检测工具: Mythril ($MYTHRIL_IMAGE)"
echo "检测对象: $CONTRACT_NAME"
echo "检测说明: 本报告基于当前主流的智能合约安全分析工具 Mythril，对目标合约进行了全面的静态安全检测，旨在发现潜在的安全风险与漏洞，提升合约的安全性与可靠性。"
echo "-----------------------------------------------------"
echo "【合约原文】"
cat "$CONTRACT_PATH"
echo "-----------------------------------------------------"
echo "【检测摘要】"

# 提取 issues 数组内容
ISSUES=$(grep -o '"issues":\[[^]]*\]' "$TMP_JSON")

if [[ "$ISSUES" == *"title"* ]]; then
	    # 有问题，逐项提取
	        echo "$ISSUES" | tr '{' '\n' | grep '"title"' | while read -r line; do
		        TITLE=$(echo "$line" | grep -o '"title": *"[^"]*"' | sed 's/.*: *"//;s/"$//')
			        SEVERITY=$(echo "$line" | grep -o '"severity": *"[^"]*"' | sed 's/.*: *"//;s/"$//')
				        HEAD=$(echo "$line" | grep -o '"head": *"[^"]*"' | sed 's/.*: *"//;s/"$//')
					        TAIL=$(echo "$line" | grep -o '"tail": *"[^"]*"' | sed 's/.*: *"//;s/"$//')
						        SWCID=$(echo "$line" | grep -o '"swcID": *"[^"]*"' | sed 's/.*: *"//;s/"$//')
							        echo "---------------------------------------------"
								        echo "问题类型: $TITLE"
									        echo "严重等级: $SEVERITY"
										        echo "描述: $HEAD"
											        echo "详细: ${TAIL:-无}"
												        echo "检测工具ID: $SWCID"
													    done
												    else
													        echo "未发现安全问题。"
fi

echo "-----------------------------------------------------"
echo "【结论说明】"
echo "本次检测报告仅供参考，检测结果基于当前工具能力及合约源码静态分析，建议结合实际业务场景及后续人工复核进一步完善安全保障。"
echo "====================================================="

rm -f "$TMP_JSON"
