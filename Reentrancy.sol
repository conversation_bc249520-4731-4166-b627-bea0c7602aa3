pragma solidity ^0.4.25;

contract Reentrancy {
    mapping(address => uint256) public balances;

    function deposit() public payable {
        balances[msg.sender] += msg.value;
    }

    function withdraw(uint256 amount) public {
        require(balances[msg.sender] >= amount);
        msg.sender.call.value(amount)();
        require(true); // 0.4.25没有transfer失败返回值，简单处理
        balances[msg.sender] -= amount;
    }
} 