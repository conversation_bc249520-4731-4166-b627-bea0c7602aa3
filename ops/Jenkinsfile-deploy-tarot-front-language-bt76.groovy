pipeline {
    agent any
    environment {
        appName =             "tarot-front"

        gitRemoteUrl =        "http://*************:8105/superai/tarot-front.git"
        gitRemoteBranch =     "*/dev_language"
        gitIdKey =            "gitlab190-jenkins190-hp"

        remoteIp =            "***********"
        remoteArchiveFolder = "/www/wwwroot/archive_home/"

        zipFileFolder =       "/var/jenkins_home/custom_home/archive_home/"
        zipFileName =         "${appName}-${new Date().format('yyyyMMdd-HHmmss')}.zip"
        zipFilePath =         "${zipFileFolder}${zipFileName}"
        remoteArchivePath =   "${remoteArchiveFolder}${zipFileName}"
    }
    tools {
        nodejs 'nodejs-20.18'
    }
    stages {
        stage('代码拉取') {
            steps {
                script {
                    echo "[${env.BUILD_ID}] - 开始SCM代码拉取..."
                    // 核心SCM拉取指令
                    checkout([
                            $class: 'GitSCM',
                            branches: [[name: "${gitRemoteBranch}"]],  // 默认主分支，可按需替换为参数化分支
                            extensions: [
                                    // 清空工作区（可选）
                                    [$class: 'CleanCheckout']
                            ],
                            userRemoteConfigs: [[
                                                        credentialsId: "${gitIdKey}",  // 需提前在Jenkins凭证管理中配置
                                                        url: "${gitRemoteUrl}"
                                                ]]
                    ])

                    // 验证代码拉取结果
                    sh '''
                        echo "当前工作目录："
                        pwd
                        echo "文件列表："
                        ls -lha
                    '''
                }
            }
        }
        stage('代码构建') {
            steps {
                script {
                    echo "[${env.BUILD_ID}] - 开始代码构建..."
                    sh '''
                        pwd
                        ls -lha
                        echo "Node版本: $(node -v)"
                        echo "NPM版本: $(npm -v)"

                        # rm -rf node_modules
                        # rm -rf dist
                        # rm -rf build
                        # npm cache clean --force

                        echo "构建开始"
                        export NODE_OPTIONS=--max_old_space_size=4096
                        npm i --registry=https://registry.npmmirror.com | tee npm-install.log
                        npm run build | tee build.log

                        echo "构建完成"
                        cd dist
                        ls -lha
                        zip -r ${zipFilePath} ./* | tee zip.log
                        ls -lha ${zipFilePath}
                    '''
                }
            }
        }
        stage('文件传输') {
            steps {
                script {
                    echo "[${env.BUILD_ID}] - 传输文件到远程服务器..."
                    sh '''
                        mkdir -p ${remoteArchiveFolder}
                        scp -o StrictHostKeyChecking=no ${zipFilePath} root@${remoteIp}:${remoteArchiveFolder}
                    '''
                }
            }
        }
        stage('远程部署') {
            steps {
                // 使用 sshagent 绑定凭据
                sshagent(['gitlab190-ssh-key']) {
                    script {
                        echo "[${env.BUILD_ID}] - 执行远程部署..."
                        sh """
                            ssh -o StrictHostKeyChecking=no root@${remoteIp} '''
                            
                                cd /www/wwwroot/tarot.zjfdsr.com
                                echo '旧文件列表如下'
                                pwd
                                ls -lha

                                echo '旧文件清理'
                                rm -rf ./*
                                echo '旧文件清理后目录'
                                ls -lha

                                echo '解压新文件'
                                unzip -o ${remoteArchivePath} -d ./
                                
                                echo '新文件列表'
                                pwd
                                sudo chattr -i ./*
                                sudo chown -R www:www ./
                                ls -lha
                            '''
                        """
                    }
                }
            }
        }
    }
    post {
        always {
            script {
                echo "[${env.BUILD_ID}] - 任务总耗时: ${currentBuild.durationString}"
            }
        }
    }
}