package com.hncboy.chatgpt.tarot.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * 塔罗牌牌义对象 tarot_card_meaning
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
public class TarotCardMeaningVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 含义
     */
    private String meaning;

    /**
     * 分类
     */
    private String tag;

    /**
     * 指引语
     */
    private String guidanceText;

    /**
     * 建议
     */
    private String advice;

    /**
     * 不建议
     */
    private String discouraged;

    /**
     * 正面图
     */
    private String cardFrontUrl;
    /**
     * 正逆位
     */
    private String position;
}