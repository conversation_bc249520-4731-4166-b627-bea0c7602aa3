package com.hncboy.chatgpt.tarot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.tarot.domain.converter.TarotCardMeaningConvert;
import com.hncboy.chatgpt.tarot.domain.dto.TarotCardMeaningDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.vo.CardSelectVO;
import com.hncboy.chatgpt.tarot.domain.vo.PositionSelectVo;
import com.hncboy.chatgpt.tarot.domain.vo.TagSelectVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import com.hncboy.chatgpt.tarot.mapper.TarotCardMeaningMapper;
import com.hncboy.chatgpt.tarot.service.TarotCardMeaningService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * 塔罗牌牌义Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
@RequiredArgsConstructor
public class TarotCardMeaningServiceImpl extends ServiceImpl<TarotCardMeaningMapper, TarotCardMeaning>
        implements TarotCardMeaningService {

    private List<PositionSelectVo> tagSelectVOS;
    /**
     * 查询塔罗牌牌义
     *
     * @param id 塔罗牌牌义主键
     * @return 塔罗牌牌义
     */
    @Override
    public TarotCardMeaningVO selectTarotCardMeaningById(Long id) {
        return baseMapper.getById(id+"");
    }

    /**
     * 查询塔罗牌牌义列表
     *
     * @param dto 塔罗牌牌义
     * @return 塔罗牌牌义
     */
    @Override
    public IPage<TarotCardMeaningVO> selectTarotCardMeaningList(TarotCardMeaningDTO dto) {
        LambdaQueryWrapper<TarotCardMeaning> wrapper = new LambdaQueryWrapper<>();
        //wrapper.eq(TarotCardMeaning::getUserId, CurrentUserUtil.getV2UserId());
        //.eq(TarotCardMeaning::getIsDelete,0);
        wrapper.like(StringUtils.isNotEmpty(dto.getName()), TarotCardMeaning::getName, dto.getName());
        wrapper.eq(StringUtils.isNotEmpty(dto.getTag()), TarotCardMeaning::getTag, dto.getTag());;
        wrapper.orderByAsc(TarotCardMeaning::getSort);
        IPage<TarotCardMeaning> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<TarotCardMeaning> iPage = this.page(userPage, wrapper);
        IPage<TarotCardMeaningVO> convert = iPage.convert(TarotCardMeaningConvert.INSTANCE::entityToVO);
        //Map<Integer, String> collect = writeAgentService.list().stream().collect(Collectors.toMap(writeAgent -> writeAgent.getId(), writeAgent -> writeAgent.getImgUrl()));

        return convert;
    }

    @Override
    public List<TagSelectVO> getSelectTagList() {
        if(tagSelectVOS==null){
            tagSelectVOS = new ArrayList<>();
            PositionSelectVo positionSelectVo = new PositionSelectVo();
            PositionSelectVo positionSelectVo1 = new PositionSelectVo();
            positionSelectVo.setKey("正位");
            positionSelectVo1.setKey("逆位");
            positionSelectVo.setValue("upright");
            positionSelectVo1.setValue("reversed");
            tagSelectVOS.add(positionSelectVo);
            tagSelectVOS.add(positionSelectVo1);
        }
        //ArrayList<PositionSelectVo>


        HashMap<String, List<CardSelectVO>> map = new HashMap<>();
        map.put("大阿卡那",new ArrayList<CardSelectVO>());
        map.put("权杖系列",new ArrayList<CardSelectVO>());
        map.put("圣杯系列",new ArrayList<CardSelectVO>());
        map.put("宝剑系列",new ArrayList<CardSelectVO>());
        map.put("星币系列",new ArrayList<CardSelectVO>());


        List<TarotCardMeaning> list = this.list();
        for (TarotCardMeaning tarotCardMeaning : list) {
            if(map.containsKey(tarotCardMeaning.getTag())){

                CardSelectVO cardSelectVO = new CardSelectVO();
                cardSelectVO.setKey(tarotCardMeaning.getName());
                cardSelectVO.setValue(tarotCardMeaning.getId().toString());
                cardSelectVO.setChildren(tagSelectVOS);

               map.get(tarotCardMeaning.getTag()).add(cardSelectVO);
            }/*else{
                List<CardSelectVO> cardSelectVOS = new ArrayList<>();
                CardSelectVO cardSelectVO = new CardSelectVO();
                cardSelectVO.setKey(tarotCardMeaning.getName());
                cardSelectVO.setValue(tarotCardMeaning.getId().toString());
                cardSelectVO.setChildren(tagSelectVOS);
                cardSelectVOS.add(cardSelectVO);
                map.put(tarotCardMeaning.getTag(),cardSelectVOS);
            }*/

        }
        ArrayList<TagSelectVO> tagSelectVOS1 = new ArrayList<>();
        int i=0;
        String[] colors = {"大阿卡那","权杖系列","圣杯系列","宝剑系列","星币系列"};

        for (String s : colors) {
            List<CardSelectVO> cardSelectVOS = map.get(s);
            TagSelectVO tagSelectVO = new TagSelectVO();
            tagSelectVO.setKey(s);
            tagSelectVO.setChildren(cardSelectVOS);
            tagSelectVO.setValue(i);
            tagSelectVOS1.add(tagSelectVO);
            i+=1;
        }

        return tagSelectVOS1;
    }

    /**
     * 新增塔罗牌牌义
     *
     * @param tarotCardMeaning 塔罗牌牌义
     * @return 结果
     */
    @Override
    public int insertTarotCardMeaning(TarotCardMeaning tarotCardMeaning) {
            return baseMapper.insert(tarotCardMeaning);
    }

    /**
     * 修改塔罗牌牌义
     *
     * @param tarotCardMeaning 塔罗牌牌义
     * @return 结果
     */
    @Override
    public int updateTarotCardMeaning(TarotCardMeaning tarotCardMeaning) {
        return baseMapper.updateById(tarotCardMeaning);
    }


    /**
     * 删除塔罗牌牌义信息
     *
     * @param id 塔罗牌牌义主键
     * @return 结果
     */
    @Override
    public int deleteTarotCardMeaningById(Long id) {
       
        return baseMapper.deleteById(id);
    }


}