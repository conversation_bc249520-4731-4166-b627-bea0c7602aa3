package com.hncboy.chatgpt.front.framework.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 支付信息 VO
 * @Version: v1.0.0
 * @Author: zyc
 * @Date: 2024/7/4
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class AlOrdersVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "产品ID")
    private BigInteger productId;

    @Schema(title = "产品类型")
    private String productType;

    @Schema(title = "产品名称")
    private String productName;

    @Schema(title = "产品价格")
    private Double productPrice;

    @Schema(title = "数量")
    private BigInteger num;

    @Schema(title = "支付状态")
    private Integer state;

    @Schema(title = "支付时间")
    private LocalDateTime payTime;

    @Schema(title = "过期时间")
    private LocalDateTime expiresTime;

    @Schema(title = "支付类型")
    private String type;

    private String tradeType;
    private String openid;

}
