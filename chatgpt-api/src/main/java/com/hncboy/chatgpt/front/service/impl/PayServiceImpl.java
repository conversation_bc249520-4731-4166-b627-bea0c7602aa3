package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.Resource;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.binarywang.wxpay.bean.marketing.transfer.*;
import com.github.binarywang.wxpay.bean.merchanttransfer.TransferCreateRequest;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyV3Result;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.google.zxing.WriterException;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.constant.OrderConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.PayCodeDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.*;
import com.hncboy.chatgpt.front.framework.domain.vo.*;
import com.hncboy.chatgpt.front.framework.enums.GroupsEnum;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.*;
import com.hncboy.chatgpt.front.helper.RedisLockHelper;
import com.hncboy.chatgpt.front.helper.UnpaidOrderQueue;
import com.hncboy.chatgpt.front.mapper.OrdersMapper;
import com.hncboy.chatgpt.front.mapper.ProductMapper;
import com.hncboy.chatgpt.front.mapper.WxPayOrderMapper;
import com.hncboy.chatgpt.front.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;



/**
 * 支付服务impl
 * 雨纷纷旧故里草木深
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayServiceImpl implements PayService {

    private final ProductMapper productMapper;
    private final RedisLockHelper lockHelper;
    private final IdGeneratorUtils idGeneratorUtils;
    private final OrdersMapper ordersMapper;
    private final UnpaidOrderQueue unpaidOrderQueue;
    private final RedisService redisService;
    private final RechargeLogService rechargeLogService;
    private final WxPayOrderService wxPayOrderService;
    private final WxPayOrderMapper wxPayOrderMapper;
    private final WxPayService wxService;
    private final WxUserInfoService wxUserInfoService;
    private final PayUtil payUtil;
    private final ITransferInfoService iTransferInfoService;
    private final UserBaseInfoService userBaseInfoService;
    private final UserPointsLogService userPointsLogService;
    private final SysConfigService sysConfigService;
    private final ISysDictDataService sysDictDataService;


    @Value("${ali-pay.appId}")
    private String appId;
    @Value("${ali-pay.alipayPublicKey}")
    private String alipayPublicKey;
    @Value("${ali-pay.privateKey}")
    private String privateKey;
    @Value("${ali-pay.domain}")
    private String domain;
    @Value("${chatOI.url}")
    private String chatOIUrl;
    private ReentrantLock[] locks;

     {
        locks = new ReentrantLock[20];
        for (int i = 0; i < 20; i++) {
            locks[i] = new ReentrantLock();
        }
    }


    /**
     * 得到产品列表
     * 产品列表
     *
     * @return {@link List}<{@link ProductVo}>
     */
    @Override
    public List<Product> getProductList(String type,String channel) {


        List<Product> products;
        QueryWrapper<Product> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(type)) {//如果type字段不为空
            wrapper.eq("type", type);
        }
        if(StringUtils.isNotEmpty(channel)) {//如果channel字段不为空
            wrapper.eq("channel", channel);
        }else{
            wrapper.isNull("channel");
        }

        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        //开始结束时间
        wrapper.le("start_time",format);
        wrapper.gt("end_time",format);
        //是否启用状态
        wrapper.eq("status","0");
        wrapper.orderByAsc("sort");

        //查询用户信息 提起用户创建信息作为条件
        try{
            Integer v2UserId = CurrentUserUtil.getV2UserId();
            UserBaseInfo info = userBaseInfoService.getUserBaseInfoByUserId(v2UserId);
            Date createTime= info.getCreateTime();
            //用户创建时间在其范围
            wrapper.le("user_reg_time_s", createTime);
            wrapper.ge("user_reg_time_e", createTime);

        }catch (Exception e){
            e.printStackTrace();
            log.info("获取用户信息失败:{}",e);
        }

        products = productMapper.selectList(wrapper);

        //防止时间段重合的相同产品名重复显示
        LinkedHashMap <String,Product> map=new LinkedHashMap <>();
        for (Product product : products) {
            if(!map.containsKey(product.getProductName())){
                map.put(product.getProductName(),product);
            }else{
                Product mapProduct = map.get(product.getProductName());
                //比较时间
                if(mapProduct.getCreateTime().isBefore(product.getCreateTime())){
                    map.put(product.getProductName(),product);
                }
            }
        }
        List<Product> newProducts=new ArrayList<>();
        map.forEach((k,v)->{
            newProducts.add(v);
        });

        return newProducts;
    }

    @Override
    public Product getProductId(Long id) {
        return productMapper.selectById(id);
    }

    /**
     * 删除产品通过id
     *
     * @param id id
     */
    @Override
    public void deleteProductById(final Long id) {
        productMapper.deleteById(id);
    }

    @Override
    public IPage<ProductVo> getProductPageVo(int pageNum, String prompt) {
        return productMapper.selectPage(new Page<>(pageNum, 15), new QueryWrapper<Product>()
                        .lambda()
                        .like(StringUtils.notEmpty(prompt), Product::getProductName, prompt)
                )
                .convert(c -> new ProductVo().setProductId(c.getProductId()).setProductName(c.getProductName())
                        .setProductPrice(c.getProductPrice()).setNum(c.getNum()).setCreateTime(c.getCreateTime()));
    }

    /**
     * 生成二维码支付
     * @Author: zyc
     * @Date: 2024-07-04
     * @param productId 产品id
     * @return {@link AlipayPayCodeVo}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AlipayPayCodeVo generatePayQrCode(final Long productId) {

        if(productId!=0){
            throw new ServiceException("当前支付方式停用,请使用微信支付");
        }

        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        
        final String timestamp = String.valueOf(System.currentTimeMillis());


        //当前登录用户ID
        final Integer currentLoginId = CurrentUserUtil.getV2UserId();
        //锁前缀
        final String lockPrefix = "ORDER_USER" + currentLoginId;
        //上锁
        final boolean lock = lockHelper.lock(lockPrefix, timestamp);
        try {
            if (!lock) {
                throw new ServiceException("请勿重复下单");
            }
            //插入支付宝logo
            Resource resource = new ClassPathResource("img/alipayLogo.png");
            BufferedImage logoImage = ImageIO.read(resource.getStream());
            final String key = OrderConstant.ORDER_PAY+ApplicationConstant.CHANNEL_ALIPAY+"-QrCode-" + currentLoginId + productId;
            if (redisService.exists(key)) {
                final AlipayCacheStructure cache = (AlipayCacheStructure) redisService.get(key);
                //生成BASE64图片给前端
                return new AlipayPayCodeVo()
                        .setOrdersId(cache.getOrdersId())
                        .setQrCode(QRCodeGenerator.generateQRCode(cache.getUrl(), logoImage))
                        .setProductType(cache.getProductType())
                        .setProductPrice(cache.getProductPrice())
                        .setProductName(cache.getProductName())
                        .setCreatedTime(cache.getCreatedTime());
            }
            //商品是否存在
            final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                    .lambda()
                    .eq(Product::getProductId, productId)
                    .le(Product::getStartTime,format)
                    .gt(Product::getEndTime,format)
                    .eq(Product::getStatus,"0")
            );
            if (product == null) {
                throw new ServiceException("商品不存在");
            }
            //生成单号
            final String orderNo = idGeneratorUtils.getOrderNo();

            Date currentTime = new Date();
            Date expiresTime = DateUtils.getDateAfter(currentTime, Calendar.MINUTE, 5);
            final Orders orders = new Orders()
                    .setOrdersId(orderNo)
                    // 0 待支付 1已支付 2 已回收
                    .setState(0)
                    .setProductPrice(product.getProductPrice())
                    .setProductName(product.getProductName())
                    .setProductType(product.getType())
                    .setProductId(productId)
                    .setNum(product.getNum())
                    .setUnit(product.getUnit())
                    .setPackageInfo(product.getPackageInfo())
                    .setUserId(currentLoginId)
                    .setExpiresTime(expiresTime)
                    .setCreatedTime(currentTime);
            ordersMapper.insert(orders);
            //装载配置
            final AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
            alipayConfig.setFormat("json");
            alipayConfig.setCharset("UTF8");
            alipayConfig.setSignType("RSA2");
            alipayConfig.setAppId(appId);
            alipayConfig.setAlipayPublicKey(alipayPublicKey);
            alipayConfig.setPrivateKey(privateKey);
            //构建支付宝订单
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //预构建请求
            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
            AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
            model.setOutTradeNo(orderNo);
            //支付金额
            model.setTotalAmount(String.valueOf(product.getProductPrice()));
            //商品名称
            model.setSubject(product.getProductName());
            //5分钟过期
            model.setTimeoutExpress("5m");
            request.setBizModel(model);
            //支付宝回调地址
            request.setNotifyUrl(domain + "/callback/order");
            AlipayTradePrecreateResponse response = alipayClient.execute(request);
            log.info("支付宝生成信息:{}", response.getBody());
            //是否构建成功？ 构建成功则 创建二维码
            if (response.isSuccess()) {
                final AlipayCacheStructure cache = new AlipayCacheStructure()
                        .setCreatedTime(orders.getCreatedTime())
                        .setProductName(product.getProductName())
                        .setProductType(product.getType())
                        .setUrl(response.getQrCode())
                        .setProductPrice(product.getProductPrice())
                        .setOrdersId(orderNo);
                //缓存订单数据
                redisService.set(key, cache, 5 * 60L);
                //添加至 待支付 队列中
                unpaidOrderQueue.addOrder(orderNo);
                //生成BASE64图片给前端
                //返回base64编码支付二维码图片
                return new AlipayPayCodeVo()
                        .setOrdersId(cache.getOrdersId())
                        .setQrCode(QRCodeGenerator.generateQRCode(cache.getUrl(), logoImage))
                        .setProductPrice(cache.getProductPrice())
                        .setProductType(cache.getProductType())
                        .setProductName(cache.getProductName())
                        .setCreatedTime(cache.getCreatedTime());
            } else {
                log.error("创建订单失败 订单号:{}, 错误信息：{}", orderNo, response.getBody());
                throw new AlipayApiException();
            }
        } catch (IOException | AlipayApiException | WriterException e) {
            throw new ServiceException("生成支付二维码失败");
        } finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AlipayPayCodeVo generatePayQrCodeNoUser(PayCodeDTO payCodeDTO) {
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

        final String timestamp = String.valueOf(System.currentTimeMillis());


        //当前登录用户ID
        final Integer currentLoginId ;
        //锁前缀
        final String lockPrefix ;
        if(payCodeDTO.getUserId() == null){
            currentLoginId = CurrentUserUtil.getV2UserId();
            lockPrefix = "ORDER_USER" + currentLoginId;
        }else{
            currentLoginId=payCodeDTO.getUserId();
            lockPrefix = "ORDER_USER" + payCodeDTO.getUserId();
        }


        //上锁
        final boolean lock = lockHelper.lock(lockPrefix, timestamp);
        try {
            if (!lock) {
                throw new ServiceException("请勿重复下单");
            }
            //插入支付宝logo
            Resource resource = new ClassPathResource("img/alipayLogo.png");
            BufferedImage logoImage = ImageIO.read(resource.getStream());
            final String key = OrderConstant.ORDER_PAY+ApplicationConstant.CHANNEL_ALIPAY+"-QrCode-" + currentLoginId + payCodeDTO.getProductId();
            if (redisService.exists(key)) {
                final AlipayCacheStructure cache = (AlipayCacheStructure) redisService.get(key);
                //生成BASE64图片给前端
                return new AlipayPayCodeVo()
                        .setOrdersId(cache.getOrdersId())
                        .setQrCode(QRCodeGenerator.generateQRCode(cache.getUrl(), logoImage))
                        .setProductType(cache.getProductType())
                        .setProductPrice(cache.getProductPrice())
                        .setProductName(cache.getProductName())
                        .setCreatedTime(cache.getCreatedTime());
            }
            //商品是否存在
            final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                    .lambda()
                    .eq(Product::getProductId, payCodeDTO.getProductId())
                    .le(Product::getStartTime,format)
                    .gt(Product::getEndTime,format)
                    .eq(Product::getStatus,"0")
            );
            if (product == null) {
                throw new ServiceException("商品不存在");
            }
            //生成单号
            final String orderNo = idGeneratorUtils.getOrderNo();

            Date currentTime = new Date();
            Date expiresTime = DateUtils.getDateAfter(currentTime, Calendar.MINUTE, 5);
            final Orders orders = new Orders()
                    .setOrdersId(orderNo)
                    // 0 待支付 1已支付 2 已回收
                    .setState(0)
                    .setProductPrice(product.getProductPrice())
                    .setProductName(product.getProductName())
                    .setProductType(product.getType())
                    .setProductId(payCodeDTO.getProductId())
                    .setNum(product.getNum())
                    .setUnit(product.getUnit())
                    .setPackageInfo(product.getPackageInfo())
                    .setUserId(currentLoginId)
                    .setExpiresTime(expiresTime)
                    .setCreatedTime(currentTime);
            ordersMapper.insert(orders);
            //装载配置
            final AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
            alipayConfig.setFormat("json");
            alipayConfig.setCharset("UTF8");
            alipayConfig.setSignType("RSA2");
            alipayConfig.setAppId(appId);
            alipayConfig.setAlipayPublicKey(alipayPublicKey);
            alipayConfig.setPrivateKey(privateKey);
            //构建支付宝订单
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //预构建请求
            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
            AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
            model.setOutTradeNo(orderNo);
            //支付金额
            model.setTotalAmount(String.valueOf(product.getProductPrice()));
            //商品名称
            model.setSubject(product.getProductName());
            //5分钟过期
            model.setTimeoutExpress("5m");
            request.setBizModel(model);
            //支付宝回调地址
            request.setNotifyUrl(domain + "/callback/order");
            AlipayTradePrecreateResponse response = alipayClient.execute(request);
            log.info("支付宝生成信息:{}", response.getBody());
            //是否构建成功？ 构建成功则 创建二维码
            if (response.isSuccess()) {
                final AlipayCacheStructure cache = new AlipayCacheStructure()
                        .setCreatedTime(orders.getCreatedTime())
                        .setProductName(product.getProductName())
                        .setProductType(product.getType())
                        .setUrl(response.getQrCode())
                        .setProductPrice(product.getProductPrice())
                        .setOrdersId(orderNo);
                //缓存订单数据
                redisService.set(key, cache, 5 * 60L);
                //添加至 待支付 队列中
                unpaidOrderQueue.addOrder(orderNo);
                //生成BASE64图片给前端
                //返回base64编码支付二维码图片
                return new AlipayPayCodeVo()
                        .setOrdersId(cache.getOrdersId())
                        .setQrCode(QRCodeGenerator.generateQRCode(cache.getUrl(), logoImage))
                        .setProductPrice(cache.getProductPrice())
                        .setProductType(cache.getProductType())
                        .setProductName(cache.getProductName())
                        .setCreatedTime(cache.getCreatedTime());
            } else {
                log.error("创建订单失败 订单号:{}, 错误信息：{}", orderNo, response.getBody());
                throw new AlipayApiException();
            }
        } catch (IOException | AlipayApiException | WriterException e) {
            throw new ServiceException("生成支付二维码失败");
        } finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxPayCodeVO generateWxPayQrCode(WxPayOrderDTO wxPayOrderDTO) {

        try {
            return wxPayOrderService.createOrder(wxPayOrderDTO);
        } catch (WxPayException e) {
            log.error("生成微信支付二维码失败：{}",e.getReturnMsg());
            throw new RuntimeException(e);
        }
        
    }



    /**
     * 支付宝回调
     *
     * @param request 请求
     * @return {@link String}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String alipayPullback(final HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            StringBuilder valueStr = new StringBuilder();
            for (int i = 0; i < values.length; i++) {
                valueStr.append((i == values.length - 1) ? values[i] : values[i] + ",");
            }
            params.put(name, valueStr.toString());
        }
        // 调用SDK验证签名
        boolean signVerified;
        try {
            signVerified = AlipaySignature.rsaCheckV1(params, alipayPublicKey, "UTF8", "RSA2");
        } catch (AlipayApiException e) {

            throw new RuntimeException(e);
        }
        // 验证成功
        if (signVerified) {

            String tradeStatus = request.getParameter("trade_status");
            log.info("支付宝回调结果:{}", tradeStatus);
            // 支付成功
            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                final String outTradeNo = request.getParameter("out_trade_no");
                final Orders orders = ordersMapper.selectOne(new QueryWrapper<Orders>()
                        .lambda()
                        .eq(Orders::getOrdersId, outTradeNo)
                );
                if (orders != null && Optional.ofNullable(orders.getState()).orElse(0) != 1) {
                    ordersMapper
                            .updateById(new Orders()
                                    .setOrdersId(outTradeNo)
                                    //已支付
                                    .setState(1)
                                    .setPayTime(new Date())
                            );
                    //根据单号，修改用户产品使用信息
                    // TODO: 逻辑存疑如此处查询是否需要进行启用控制
                    final Product product = productMapper.selectOne(new QueryWrapper<Product>().lambda()
                            .eq(Product::getProductId, orders.getProductId()));
                    //根据情况对绘画 和 对话次数进行添加
                    if (product != null) {
                        //充值处理
                        payUtil.chargeUp(product, orders);
                        //生成充值记录
                        final RechargeLog rechargeLog = new RechargeLog()
                                .setUserId(orders.getUserId()).setOrderId(orders.getOrdersId()).setChannel(ApplicationConstant.CHANNEL_ALIPAY)
                                .setProductId(orders.getProductId()).setRechargeTime(orders.getPayTime())
                                .setPackageInfo(orders.getPackageInfo())
                                .setRechargeAmt(orders.getProductPrice()).setTimes(Math.toIntExact(product.getNum()))
                                .setUnit(orders.getUnit()).setStartTime(orders.getCreatedTime())
                                .setEndTime(orders.getUpdateTime())
                                .setCreatedTime(new Date());
                        rechargeLogService.insert(rechargeLog);

                        //积分记录
                        payUtil.addRechargePoints(product, orders);
                    }

                    redisService.remove(OrderConstant.ORDER_PAY +ApplicationConstant.CHANNEL_ALIPAY + orders.getUserId().toString() + orders.getProductId());
                }

                return "success";
            }
        } else {
            log.error("支付失败");
            return "fail";
        }
        return "fail";
    }    
    /**
     * 微信回调
     *
     * @param request 请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void wxPullback(Map body, HttpServletRequest request) {

        Object mchId = request.getAttribute("mchId");
        if(ObjectUtil.isEmpty(mchId)){
            throw new RuntimeException("无效的商户号");
        }
        wxService.switchover(mchId.toString());

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                //官方文档中有说明切记不要改变原始报文主体，如果使用JSONObject接收的话，不能使用JSON转换出来的字符串，会出现验签错误的情况，请注意
                String data = objectMapper.writeValueAsString(body);
                SignatureHeader header = new SignatureHeader();
                header.setTimeStamp(request.getHeader("Wechatpay-Timestamp"));
                header.setNonce(request.getHeader("Wechatpay-Nonce"));
                header.setSignature(request.getHeader("Wechatpay-Signature"));
                header.setSerial(request.getHeader("Wechatpay-Serial"));
                WxPayOrderNotifyV3Result wxPayOrderNotifyV3Result = wxService.parseOrderNotifyV3Result(data, header);


                WxPayOrderNotifyV3Result.DecryptNotifyResult result = wxPayOrderNotifyV3Result.getResult();//解密后的数据

                log.info("微信支付回调结果:{}", result);

                final String outTradeNo = result.getOutTradeNo();
                int hash = Math.abs(outTradeNo.hashCode() %  locks.length);

                locks[hash].lock();
                try{
                    if ("SUCCESS".equalsIgnoreCase(result.getTradeState())) {
                        //log.info("支付成功----------------------------------");
                        //sleep(20000);
                        final WxPayOrder orders = wxPayOrderMapper.selectOne(new QueryWrapper<WxPayOrder>()
                                .lambda()
                                .eq(WxPayOrder::getOutTradeNo, outTradeNo)
                                .eq(WxPayOrder::getStatus, 0)
                        );
                        if (orders != null && Optional.ofNullable(orders.getStatus()).orElse(0) != 1) {
                            //String dateString = "2023-04-05T15:20:30+08:00"; // 示例日期字符串
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
                            Date date = sdf.parse(result.getSuccessTime());


                            wxPayOrderMapper
                                    .updateById(new WxPayOrder()
                                            .setId(orders.getId())
                                            .setOutTradeNo(outTradeNo)
                                            //已支付
                                            .setStatus(1)
                                            .setTimeEnd(date)
                                            .setTransactionId(result.getTransactionId())
                                            .setOriginalMessage(body.toString())
                                    );
                            //根据单号，修改用户产品使用信息
                            // TODO: 逻辑存疑如此处查询是否需要进行启用控制
                            final Product product = productMapper.selectOne(new QueryWrapper<Product>().lambda()
                                    .eq(Product::getProductId, orders.getGoodsId()));
                            //根据情况对绘画 和 对话次数进行添加
                            if (product != null) {
                                //充值处理
                                Orders orders1 = new Orders().setOrdersId(orders.getOutTradeNo())
                                        .setProductId(orders.getGoodsId().longValue())
                                        .setUserId(orders.getUserId())
                                        .setUnit(orders.getUnit())
                                        .setNum(orders.getNum());

                                log.info("充值处理, 用户: {}, 订单号: {}, 产品类型: {}, 数量: {}"
                                        , orders.getUserId(), orders1.getOrdersId(), product.getType(), orders.getNum());
                                payUtil.chargeUp(product, orders1);
                                //生成充值记录
                                final RechargeLog rechargeLog = new RechargeLog()
                                        .setUserId(orders.getUserId()).setOrderId(orders.getOutTradeNo())
                                        .setChannel(ApplicationConstant.CHANNEL_WX)
                                        .setProductId(orders.getGoodsId().longValue())
                                        .setRechargeTime(date)
                                        .setPackageInfo(product.getPackageInfo())
                                        .setRechargeAmt(product.getProductPrice()).setTimes(Math.toIntExact(product.getNum()))
                                        .setUnit(orders.getUnit()).setStartTime(orders.getCreateTime())
                                        .setEndTime(orders.getUpdateTime())
                                        .setCreatedTime(new Date());
                                rechargeLogService.insert(rechargeLog);

                                //积分记录
                                payUtil.addRechargePoints(product, orders1);
                            }
                            log.info("-------------订单支付成功，已处理-------------");
                            log.info(OrderConstant.ORDER_PAY+ApplicationConstant.CHANNEL_WX+"-"+orders.getTradeType() +"-"+ orders.getUserId().toString() + orders.getGoodsId());
                            redisService.remove(OrderConstant.ORDER_PAY+ApplicationConstant.CHANNEL_WX+"-"+orders.getTradeType() +"-"+ orders.getUserId().toString() + orders.getGoodsId());

                            //判断当前产品是否是chatoi产品
                            checkChatOIGroup(product, orders);

                        }
                        else{
                            log.info("-------------订单已支付，无需重复处理-------------");
                            log.info("订单详情：{}",orders);
                        }


                    }
                }finally {
                    locks[hash].unlock();
                }


            } catch (Exception e) {
                log.error("微信支付回调内容解析失败", e);
                throw new RuntimeException(e.getMessage(), e);
            }
        } catch (Exception e) {

            log.error("微信支付成功回调失败", e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    private void checkChatOIGroup(Product product, WxPayOrder orders) {
        try {
            if (product != null && "chatoi".equals(product.getChannel())) {
                // 根据用户id获取用户信息
                UserBaseInfo byId = userBaseInfoService.getById(orders.getUserId());
                //获取当前用户信息

                String userId = HttpUtil.get(chatOIUrl+"users/user/info/"+byId.getAccount()).replace("\"","");
                String res = HttpUtil.get(chatOIUrl+"groups/"+userId);

                Map<String, String> map = this.JsonToMap(res);
                Set<String> strings = map.keySet();

                List<SysDictData> sysDictData = sysDictDataService.selectDictLabel("chatoi_groups");
                HashMap<String, String> set = new HashMap<>();
                for (SysDictData sysDictDatum : sysDictData) {
                    set.put(sysDictDatum.getDictLabel(),sysDictDatum.getDictValue());
                }
                //通过商品信息判断当前是哪个组 且需要判断当前用户是哪个组的
                if(product.getProductName().contains("一个月")){
                    //判断当前用户所属组是否大于当前组
                    if(!strings.contains(GroupsEnum.ONE_MONTH.getCode())){
                        if(!(strings.contains(GroupsEnum.THREE_MONTHS.getCode())||strings.contains(GroupsEnum.SIX_MONTHS.getCode())||strings.contains(GroupsEnum.ONE_YEAR.getCode()))){
                            this.updateUserGroup(set.get(GroupsEnum.ONE_MONTH.getCode()),userId);
                            //如果小于当前组则将当前组更新为商品所属组
                        }
                    }
                }else if(product.getProductName().contains("三个月")){
                    //判断当前用户所属组是否大于当前组
                    if(!strings.contains(GroupsEnum.THREE_MONTHS.getCode())){
                        if(!(strings.contains(GroupsEnum.SIX_MONTHS.getCode())||strings.contains(GroupsEnum.ONE_YEAR.getCode()))){
                            //如果小于当前组则将当前组更新为商品所属组
                            this.updateUserGroup(set.get(GroupsEnum.THREE_MONTHS.getCode()),userId);
                        }
                    }

                }else if(product.getProductName().contains("六个月")){
                    //判断当前用户所属组是否大于当前组
                    if(!strings.contains(GroupsEnum.SIX_MONTHS.getCode())){
                        if(!strings.contains(GroupsEnum.ONE_YEAR.getCode())){
                            //如果小于当前组则将当前组更新为商品所属组
                            this.updateUserGroup(set.get(GroupsEnum.SIX_MONTHS.getCode()),userId);
                        }
                    }

                }else if(product.getProductName().contains("一年")){
                    //判断当前用户所属组是否大于当前组
                    if(!strings.contains(GroupsEnum.ONE_YEAR.getCode())){
                        this.updateUserGroup(set.get(GroupsEnum.ONE_YEAR.getCode()),userId);
                    }
                }
            }
        }catch (Exception e){
            log.error("用户充值设置分组失败错误原因：",e);
        }
    }

    private void updateUserGroup(String groupId, String userId) {
        String req = chatOIUrl+"groups/id/"+groupId+"/update/"+userId;
        log.info("变更用户分组请求为{}",req);
        String rest = HttpUtil.post(req, (String) null);
        log.info("变更结果为{}",rest);
    }
    /**
     * 付款状态
     * 0 待支付 1已支付 2 已回收
     *
     * @param orderNo 订单没有
     * @return {@link String}
     */
    @Override
    public PayOutComeVo paymentStatus(final String orderNo) {
        final Orders orders = ordersMapper.selectOne(new QueryWrapper<Orders>()
                        .lambda()
                        .eq(Orders::getOrdersId, orderNo)
                        .select(Orders::getState,Orders::getProductId)

        );
        // TODO: 逻辑存疑如此处查询是否需要进行启用控制
        final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                .lambda().eq(Product::getProductId, orders.getProductId())
                .select(Product::getProductId, Product::getProductPrice, Product::getProductName
                        , Product::getRemark, Product::getType, Product::getUnit, Product::getNum)
        );
        PayOutComeVo payOutComeVo = new PayOutComeVo();
        BeanUtils.copyProperties(product, payOutComeVo);

        if (orders != null) {
            if (orders.getState() == 0) {
                payOutComeVo.setPayStatus(OrderConstant.BE_PAID);
                return payOutComeVo;
            } else if (orders.getState() == 1) {
                payOutComeVo.setPayStatus(OrderConstant.PAID);
                return payOutComeVo;
            } else {

                payOutComeVo.setPayStatus(OrderConstant.IS_CLOSED);
                return payOutComeVo;
            }
        } else {
            payOutComeVo.setPayStatus(OrderConstant.IS_CLOSED);
            return payOutComeVo;
        }

    }

    @Override
    public PayOutComeVo createPrepayOrder(PayCodeDTO payCodeDTO) {

        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

        final String timestamp = String.valueOf(System.currentTimeMillis());
        //锁前缀
        final String lockPrefix;
        final Integer currentLoginId;
        //当前登录用户ID
        if(payCodeDTO.getUserId() == null){
            currentLoginId = CurrentUserUtil.getV2UserId();
            lockPrefix = "ORDER_USER" + currentLoginId;
        }else{
            currentLoginId=payCodeDTO.getUserId();
            lockPrefix = "ORDER_USER" + payCodeDTO.getUserId();
        }

        //上锁
        final boolean lock = lockHelper.lock(lockPrefix, timestamp);
        try {
            if (!lock) {
                throw new ServiceException("请勿重复下单");
            }
            final String key = OrderConstant.ORDER_PAY+ApplicationConstant.CHANNEL_ALIPAY +"-PayOrder-" +currentLoginId + payCodeDTO.getProductId();
            if (redisService.exists(key)) {
                //final PayOutComeVo cache =
                //生成BASE64图片给前端
                return (PayOutComeVo) redisService.get(key);
            }


            final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                    .lambda()
                    .eq(Product::getProductId, payCodeDTO.getProductId())
                    .le(Product::getStartTime,format)
                    .gt(Product::getEndTime,format)
                    .eq(Product::getStatus,"0")
                    .select(Product::getProductId, Product::getProductPrice, Product::getNum,Product::getType,
                            Product::getUnit, Product::getProductName, Product::getPackageInfo,Product::getRemark)
            );
            if (product == null) {
                throw new ServiceException("商品不存在");
            }
            final String orderNo = idGeneratorUtils.getOrderNo();
            Date currentTime = new Date();
            Date expiresTime = DateUtils.getDateAfter(currentTime, Calendar.MINUTE, 5);
            final Orders orders = new Orders()
                    .setOrdersId(orderNo)
                    // 0 待支付 1已支付 2 已回收
                    .setState(0)
                    .setProductPrice(product.getProductPrice())
                    .setProductName(product.getProductName())
                    .setProductType(product.getType())
                    .setProductId(product.getProductId())
                    .setNum(product.getNum())
                    .setUnit(product.getUnit())
                    .setPackageInfo(product.getPackageInfo())
                    .setUserId((payCodeDTO.getUserId()==null)?CurrentUserUtil.getV2UserId():payCodeDTO.getUserId())
                    .setExpiresTime(expiresTime)
                    .setCreatedTime(currentTime);
            ordersMapper.insert(orders);

            //构建支付宝订单
            AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do",appId,privateKey,"json","utf-8",alipayPublicKey,"RSA2");
            AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
            //异步接收地址，仅支持http/https，公网可访问
            request.setNotifyUrl(domain + "/callback/order");
            //同步跳转地址，仅支持http/https
            //request.setReturnUrl("");
            /******必传参数******/
            JSONObject bizContent = new JSONObject();
            //商户订单号，商家自定义，保持唯一性
            bizContent.put("out_trade_no",orderNo);
            //支付金额，最小值0.01元
            bizContent.put("total_amount", product.getProductPrice());
            //订单标题，不可使用特殊符号
            bizContent.put("subject", product.getProductName());

            /******可选参数******/
            //手机网站支付默认传值QUICK_WAP_WAY
            bizContent.put("product_code", "QUICK_WAP_WAY");
            //bizContent.put("time_expire", expiresTime);


            request.setBizContent(bizContent.toString());
            AlipayTradeWapPayResponse response;
            try {
                //response = alipayClient.pageExecute(request,"POST");
                response = alipayClient.pageExecute(request,"GET");
            } catch (AlipayApiException e) {
                throw new RuntimeException(e);
            }
            // 如果需要返回GET请求，请使用
            String pageRedirectionData = response.getBody();
            System.out.println(pageRedirectionData);
            log.info("支付宝支付订单生成：{}",pageRedirectionData);

            PayOutComeVo payOutComeVo = new PayOutComeVo();
            payOutComeVo.setRemark(pageRedirectionData);
            redisService.set(key, payOutComeVo, 5 * 60L);
            //unpaidOrderQueue.addOrder(orderNo);

            return payOutComeVo;
        } finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }

    }

    /**
     * 创建提现订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R createPayouts(TransferInfo transferInfo) {
        if(transferInfo.getTransferPoints()<5){
            return R.fail("提现最少需要提现5奖励金");
        }


        SysConfig testPhone  = sysConfigService.querySysConfig("withdrawal_black");;

        //获取当前用户id
        final Integer currentLoginId = CurrentUserUtil.getV2UserId();
        //判断当前用户奖励金是否大于等于需要提现的奖励金数目
        UserBaseInfo userBaseInfoByUserId = userBaseInfoService.getUserBaseInfoByUserId(currentLoginId);

        if(testPhone.getConfigValue().contains(userBaseInfoByUserId.getAccount())){
            return R.fail("内部账号不能提现");
        }

        Integer i = userPointsLogService.checkPointsLog(currentLoginId);

        WxUserInfoVO wxUserInfoVO = wxUserInfoService.queryUserInfoByOpenId(userBaseInfoByUserId.getOpenId());
        if(wxUserInfoVO==null){
            return R.fail("请绑定微信后进行提现");
        }

        final String lockPrefix ;
        lockPrefix = "PAY_PAYOUTS" + currentLoginId;
        final String timestamp = String.valueOf(System.currentTimeMillis());

        //上锁
        final boolean lock = lockHelper.lock(lockPrefix, timestamp);
        try {
            if(i>=transferInfo.getTransferPoints()){
                //如果大于等于则创建提现订单
                if (!lock) {
                    return R.fail("请勿重复提现");
                    //throw new ServiceException("请勿重复提现");
                }

                final String orderNo = idGeneratorUtils.getOrderNo();
                transferInfo.setUserId(currentLoginId);
                transferInfo.setNickName(userBaseInfoByUserId.getNickName());
                transferInfo.setOpenId(userBaseInfoByUserId.getOpenId());
                transferInfo.setStatus("0");
                transferInfo.setTransferRemark("奖励金提现");
                transferInfo.setTransferAmount(transferInfo.getTransferPoints());
                //transferInfo.setTransferStartTime(new Date());
                transferInfo.setOutDetailNo(orderNo);

                //创建提现订单
                iTransferInfoService.insertTransferInfo(transferInfo);

                //更新当前用户奖励金
                UserPointsLog userPointsLog = new UserPointsLog();
                userPointsLog.setUserId(currentLoginId);
                userPointsLog.setPoints(transferInfo.getTransferPoints()*-1);
                userPointsLog.setRemark("奖励金提现");
                userPointsLog.setPointsType("payouts_points");
                userPointsLog.setRelOrder(orderNo);
                userPointsLog.setCreateBy(currentLoginId+"");
                userPointsLog.setUpdateBy(currentLoginId+"");

                //更新当前用户奖励金
                userPointsLogService.save(userPointsLog);

                //扣除用户奖励金
                userBaseInfoService.updateUserPoints(currentLoginId,userBaseInfoByUserId.getPoints()- transferInfo.getTransferPoints());


            }else{
                //如果小于则返回 当前奖励金不足
                //throw new ServiceException("当前奖励金不足");
                return R.fail("当前奖励金不足");

            }
        }finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }
        return R.success("提现成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payouts() {

        //加锁同一时间内只能执行一次
        final String lockPrefix = "PAY_PAYOUTS";
        final String timestamp = String.valueOf(System.currentTimeMillis());
        boolean lock = lockHelper.lock(lockPrefix, timestamp);


        try {
            if (!lock) {
                log.error("请勿重复发起提交任务");
                throw new ServiceException("请勿重复发起提交任务");
            }

            boolean flag = true;
            while (flag) {
                //获取所有待提现订单
                List<TransferInfo> transferInfos = iTransferInfoService.list(new QueryWrapper<TransferInfo>()
                        .eq("status", "0")
                        .last("limit 950"));

                if(transferInfos.isEmpty()){
                    flag=false;
                    log.info("当前没有待提现订单");
                }else{
                    TransferCreateRequest request = new TransferCreateRequest();
                    List<TransferCreateRequest.TransferDetailList> transferDetailList = new ArrayList<>();//request.getTransferDetailList();
                    //transferInfos transferDetailList
                    final String orderNo = idGeneratorUtils.getOrderNo();
                    Date date = new Date();
                    int sum=0;
                    for (TransferInfo transferInfo : transferInfos) {
                        transferInfo.setOutBatchNo(orderNo);
                        transferInfo.setOutBatchTime(date);
                        //TODO 微信提现测试 测试数据需要修改
                        sum+=1;//transferInfo.getTransferPoints();
                        TransferCreateRequest.TransferDetailList transferDetail = new TransferCreateRequest.TransferDetailList();
                        BeanUtils.copyProperties(transferInfo,transferDetail);
                        transferDetail.setTransferAmount(1);
                        transferDetail.setUserName(null);
                        transferDetail.setOpenid(transferInfo.getOpenId());
                        transferDetailList.add(transferDetail);
                        request.setTransferDetailList(transferDetailList);
                        transferInfo.setStatus("1");

                    }
                    request.setOutBatchNo(orderNo);
                    request.setBatchName(DateUtil.format(date,"yyyyMMd")+"奖励金提现");
                    request.setBatchRemark("奖励金提现");
                    request.setTotalAmount(sum);
                    request.setTotalNum(transferDetailList.size());

                    try {
                        this.wxService.getMerchantTransferService().createTransfer(request);
                    } catch (WxPayException e) {
                        log.error("提现异常", e);
                        throw new RuntimeException(e);
                    }

                    //更新提现订单状态
                    iTransferInfoService.updateBatchById(transferInfos);

                }
            }

        }finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }


        //获取所有待提现订单

    }

    @Override
    public void checkPayoutsOrder() {

        List<TransferInfo> list = iTransferInfoService.list(new QueryWrapper<TransferInfo>()
                .eq("status", "1")
                .groupBy("out_batch_no")
                .select("out_batch_no")
        );
        try {
            for (TransferInfo transferInfo : list) {
                MerchantBatchRequest merchantBatchRequest = new MerchantBatchRequest();
                merchantBatchRequest.setOutBatchNo(transferInfo.getOutBatchNo());
                BatchNumberResult batchNumberResult = this.wxService.getPartnerTransferService().queryBatchByOutBatchNo(merchantBatchRequest);
                /**
                 * WAIT_PAY: 待付款确认。需要付款出资商户在商家助手小程序或服务商助手小程序进行付款确认
                 * ACCEPTED:已受理。批次已受理成功，若发起批量转账的30分钟后，转账批次单仍处于该状态，可能原因是商户账户余额不足等。商户可查询账户资金流水，若该笔转账批次单的扣款已经发生，则表示批次已经进入转账中，请再次查单确认
                 * PROCESSING:转账中。已开始处理批次内的转账明细单
                 * FINISHED:已完成。批次内的所有转账明细单都已处理完成
                 * CLOSED:已关闭。可查询具体的批次关闭原因确认
                 */
                boolean flag = true;
                switch (batchNumberResult.getBatchStatus()) {
                    case "WAIT_PAY":
                        flag=false;
                        log.info("待付款确认");
                        break;
                    case "ACCEPTED":
                        flag=false;
                        log.info("已受理");
                        break;
                    case "PROCESSING":
                        log.info("转账中");
                        flag=false;
                        break;
                    case "FINISHED":
                        log.info("已完成");
                        break;
                    case "CLOSED":
                        log.info("已关闭");
                        flag=false;
                        break;
                }
                if(flag){
                    /**
                     * INIT: 初始态。 系统转账校验中
                     * WAIT_PAY: 待确认。待商户确认, 符合免密条件时, 系统会自动扭转为转账中
                     * PROCESSING:转账中。正在处理中，转账结果尚未明确
                     * SUCCESS:转账成功
                     * FAIL:转账失败。需要确认失败原因后，再决定是否重新发起对该笔明细单的转账（并非整个转账批次单）
                     */
                    ArrayList<TransferInfo> transferInfos = new ArrayList<>();
                    for (BatchNumberResult.TransferDetail transferDetail : batchNumberResult.getTransferDetailList()) {
                        TransferInfo transferInfo1 = new TransferInfo();
                        transferInfo1.setOutDetailNo(transferDetail.getOutDetailNo());
                        switch (transferDetail.getDetailStatus()) {
                            case "SUCCESS":
                                transferInfo1.setStatus("2");
                                iTransferInfoService.update(new UpdateWrapper<TransferInfo>().set("status", "2").eq("out_detail_no",transferDetail.getOutDetailNo()));
                                break;
                            case "FAIL":
                                log.info("转账失败");
                                iTransferInfoService.update(new UpdateWrapper<TransferInfo>().set("status", "9").eq("out_detail_no",transferDetail.getOutDetailNo()));
                                //transferInfo1.setStatus("9");
                                break;
                        }
                        transferInfos.add(transferInfo1);
                    }
                }
            }
        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }
    }

    public Map<String,String> JsonToMap(String json){
        //字符串
        JSONArray objects = JSONUtil.parseArray(json);
        HashMap<String, String> map = new HashMap<>();
        for (Object object : objects) {
            cn.hutool.json.JSONObject entries = JSONUtil.parseObj(object);
            //Object o = entries.get("id");
            //System.out.println("o = " + o);
            map.put(entries.get("name").toString(),entries.get("id").toString());

        }

        System.out.println("map = " + map);
        return map;
    }



}

