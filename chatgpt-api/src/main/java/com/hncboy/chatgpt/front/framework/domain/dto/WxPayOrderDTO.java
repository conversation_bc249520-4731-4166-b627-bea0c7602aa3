package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 支付订单信息 DTO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
@Data
public class WxPayOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "商品ID")
    private Integer goodsId;
    
    

    @Schema(title = "商品描述")
    private String body;

    @Schema(title = "商户订单号")
    private String outTradeNo;

    @Schema(title = "总金额")
    private Double totalFee;

    @Schema(title = "状态")
    private Integer status;

    @Schema(title = "终端IP")
    private String spbillCreateIp;

    @Schema(title = "通知地址")
    private String notifyUrl;

    @Schema(title = "交易类型")
    private String tradeType;

    @Schema(title = "用户标识")
    private String openid;

    @Schema(title = "原始数据")
    private String originalMessage;

    @Schema(title = "是否关注公众账号")
    private String isSubscribe;

    @Schema(title = "微信支付订单号")
    private String transactionId;

    @Schema(title = "支付完成时间")
    private Date timeEnd;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;

    @Schema(title = "更新者")
    private String updateBy;

    @Schema(title = "更新时间")
    private LocalDateTime updateTime;

    @Schema(title = "备注")
    private String remark;
    private Date expiresTime;

    /**
     * 用户ID
     */
    private Integer userId;


    /**
     * 购买数量
     */
    private Long num;

    /**
     * 单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)
     */
    private String unit;
    /**
     * H5 环境类型  IOS or Android
     */
    @Schema(title = "环境类型")
    private String type;

    private String productType;

    private String userValue;
    private String appId;

}
