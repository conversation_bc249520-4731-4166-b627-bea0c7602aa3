package com.hncboy.chatgpt.front.framework.domain.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户基础信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_base_info")
public class UserBaseInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private Integer id;

    /**
     * 账号(手机号或其他账号)
     */
    private String account;

    /**
     * 用户名
     */
    private String name;

    /**
     * 昵称
     */
    private String nickName;

    private String wxUserName;

    /**
     * 最后登录时间
     */
    private LocalDateTime loginTime;


    /**
     * IP地址
     */
    private String ip;


    /**
     * 头像
     */
    private String headSculpture;


    /**
     * 地址
     */
    private String address;


    /**
     * 邮箱
     */
    private String email;


    /**
     * 状态0正常1禁用
     */
    private Integer status;


    /**
     * 剩余可用次数(充值)
     */
    private Integer useNum;


    /**
     * 绘画次数
     */
    private Integer drawNum;


    /**
     * 音乐创作次数
     */
    private Integer musicNum;


    /**
     * 写作次数
     */
    private Integer writeNum;



    /**
     * 免费可用次数(赠送)
     */
    private Integer freeNum;


    /**
     * 每日免费次数
     */
    private Integer dailyFreeTime;


    /**
     * 上级ID
     */
    private Integer parentId;


    /**
     * 微信openId
     */
    private String openId;


    /**
     * 积分
     */
    private  Integer points;

    /**
     * 会员到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime vipEndTime;

    /**
     * 登录TOKEN
     */
    private String token;
    
    private String firstStatus;

    private String userValue;

    /**
     * 今日是否签到
     */
    private String checkUser;
    /**
     * 今日指引id
     */
    private String insightId;

    /**
     * 塔罗币
     */
    private Integer tarotCoins;


    /**
     * 当前时间
     */
    private String currentTime;

    /**
     * 用户类型 zns:智能社 tarot:塔罗
     */
    private String userType;

    private SiteInfo siteInfo;

    private Date createTime;

    private Integer commissionId;

    private String commissionType;

    private String wxMpUrl ;


}
