package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.dto.UserBaseInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;

/**
 * 用户基础信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
public interface UserBaseInfoService extends IService<UserBaseInfo> {


    /**
     * 根据用户ID获取用户基础信息
     *
     * @param userId 用户ID
     * @return 用户基础信息
     */
    UserBaseInfoVO queryUserInfoById(Integer userId);

    UserBaseInfoVO queryUserInfoByPhone(String phoneNumber);


    /**
     * 修改用户登录时间
     *
     * @param userId
     * @return void
     * @Author: zc.wu
     * @Date: 2024/3/1 0001 下午 03:26
     */
    void updateUserLoginTime(Integer userId);


    /**
     * 根据账号获取用户基础信息
     *
     * @param account 账号
     * @return 用户基础信息
     */
    UserBaseInfoVO getUserBaseInfoByAccount(String account);


    /**
     * 初始化用户信息
     *
     * @param loginInfoParam 登录信息
     */
    UserBaseInfoVO initUserInfo(LoginInfoParam loginInfoParam);
    UserBaseInfoVO initUserInfo2(LoginInfoParam loginInfoParam);


    /**
     * 增加用户积分
     *
     * @param parentNum
     * @param phone
     */
    void addUserPoints(Integer parentNum, String phone,String Type);

    /**
     * 扣除用户可用次数
     *
     * @param userId  用户ID
     * @param species 次数
     * @param type    1 收费次数 2免费次数
     */
    void updateUserNum(Integer userId, Integer species, Integer type);


    /**
     * 增加用户可用次数
     *
     * @param userId
     * @param species
     * @return void
     * @Author: zc.wu
     * @Date: 2024/2/26 0026 下午 01:48
     */
    void addUserNum(Integer userId, Integer species);


    /**
     * 扣减绘画可用次数
     *
     * @param userId
     * @param drawNum
     * @return void
     * @Author: zc.wu
     * @Date: 2024/2/26 0026 下午 01:48
     */
    void updateUserDrawNumMp(Integer userId, Integer drawNum);

    /**
     * 扣减音乐创作可用次数
     *
     * @param userId
     * @param musicNum
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/4/25 14:51
     */
    void updateUserMusicNumMp(Integer userId, Integer musicNum);

    /**
     * 扣减写作可用次数
     *
     * @param userId
     * @param writeNum
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/7/2 14:51
     */
    void updateUserWriteNumMp(Integer userId, Integer writeNum);

    /**
     * 扣减塔罗牌可用次数
     * @param userId
     * @param TarotNum
     */
    void updateUserTarotNumMp(Integer userId, Integer TarotNum);

    /**
     * 增加用户vip时间
     *
     * @param userId
     * @param num
     */
    void updateVipTime(Integer userId, String unit, Long num);


    /**
     * 效验用户是否为VIp
     *
     * @param
     * @return boolean
     * @Author: zc.wu
     * @Date: 2024/3/27 15:40
     */
    boolean checkUserIsVip();

    boolean checkUserIsVip(UserBaseInfoVO userBaseInfo);

    /**
     * 通过id获取用户信息
     * @param userId
     * @return
     */
    UserBaseInfo getUserBaseInfoByUserId(Integer userId);

    /**
     * 根据用户id 来修改积分
     * @param userId
     * @param newPoints
     */
    void updateUserPoints(Integer userId, Integer newPoints);

    /**
     * 根据用户id 修改塔罗币
     * @param userId
     * @param newPoints
     */
    void updateUserTarotCoins(Integer userId, Integer newPoints);

    /**
     * 根据id添加用户绘画次数
     * @param userId
     * @param times
     */
    void addUserDrawNumMp(Integer userId, Integer times);


    /**
     * 根据id添加用户音乐创作可用次数
     * @param userId
     * @param times
     */
    void addUserMusicNumMp(Integer userId, Integer times);

    /**
     * 根据id添加用户写作点数
     * @param userId
     * @param times
     */
    void addUserWriteNumMp(Integer userId, Integer times);

    /**
     * 根据id添加用户塔罗币
     * @param userId
     * @param times
     */
    void addUserTarotNumMp(Integer userId, Integer times);

    /**
     * 根据id设置父级id
     * @param id
     * @param parentId
     */
    void setParentId(Integer id, Integer parentId);

    /**
     * 根据id更新头像和昵称
     * @param userInfoDTO
     */
    void updateUserInfoById(UserBaseInfoDTO userInfoDTO);


    /**
     * 根据openId获取用户信息
     * @param openId
     * @return
     */
    UserBaseInfoVO getUserBaseInfoByOpenId(String openId);

    /**
     * 根据Id更新账号（手机号）
     * @param id
     * @param phone
     * @return
     */
    void updateUserAccountById(Integer id, String phone);
    /**
     * 修改首次登录状态
     * @param id
     * @return
     */
    void updateUserFirstStatusById(Integer id);

    String getParentNum(String parentId);

    void updateUserInfoIsDeleteById(Integer id);

    boolean isOnlyOneUserBaseInfoRecord(String openid);
}
