package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.Resource;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.api.AlipayApiException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.google.zxing.WriterException;
import com.hncboy.chatgpt.front.framework.config.WxPayProperties;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.constant.OrderConstant;
import com.hncboy.chatgpt.front.framework.converter.WxPayOrderConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.Orders;
import com.hncboy.chatgpt.front.framework.domain.entity.Product;
import com.hncboy.chatgpt.front.framework.domain.entity.WxPayOrder;
import com.hncboy.chatgpt.front.framework.domain.vo.*;
import com.hncboy.chatgpt.front.framework.enums.MemberEnum;
import com.hncboy.chatgpt.front.framework.enums.OrderStatusEnum;
import com.hncboy.chatgpt.front.framework.enums.WxMchIdEnum;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.*;
import com.hncboy.chatgpt.front.helper.RedisLockHelper;
import com.hncboy.chatgpt.front.helper.RestTemplateUtil;
import com.hncboy.chatgpt.front.helper.UnpaidOrderQueue;
import com.hncboy.chatgpt.front.mapper.OrdersMapper;
import com.hncboy.chatgpt.front.mapper.ProductMapper;
import com.hncboy.chatgpt.front.mapper.WxPayOrderMapper;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.WxPayOrderService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import static com.hncboy.chatgpt.front.framework.util.CurrentUserUtil.getUserId;

/**
 * 支付订单信息实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WxPayOrderServiceImpl extends ServiceImpl<WxPayOrderMapper, WxPayOrder> implements WxPayOrderService {

    @Autowired
    private WxUserInfoService wxUserInfoService;

    private final WxPayService wxService;
    private final RedisLockHelper lockHelper;

    private final IdGeneratorUtils idGeneratorUtils;
    private final ProductMapper productMapper;
    @Value("${wx.pay.notifyUrl}")
    private String notifyUrl;
    private final RedisService redisService;
    //private final UnpaidOrderQueue unpaidOrderQueue;

    /**
     * 根据订单号查询
     *
     * @param order
     * @return WxPayOrderVO
     * @Author: zc.wu
     * @Date: 2023/4/21 14:20
     */
    @Override
    public PayOutComeVo queryOrderBySn(String order) {

        final WxPayOrder orders = this.getOne(new QueryWrapper<WxPayOrder>()
                .lambda()
                .eq(WxPayOrder::getOutTradeNo, order)

        );
        // TODO: 逻辑存疑如此处查询是否需要进行启用控制
        final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                .lambda().eq(Product::getProductId, orders.getGoodsId())
        );
        PayOutComeVo payOutComeVo = new PayOutComeVo();
        BeanUtils.copyProperties(product, payOutComeVo);

        if (orders != null) {
            if (orders.getStatus() == 0) {
                payOutComeVo.setPayStatus(OrderConstant.BE_PAID);
                return payOutComeVo;
            } else if (orders.getStatus() == 1) {
                payOutComeVo.setPayStatus(OrderConstant.PAID);
                return payOutComeVo;
            } else {

                payOutComeVo.setPayStatus(OrderConstant.IS_CLOSED);
                return payOutComeVo;
            }
        } else {
            payOutComeVo.setPayStatus(OrderConstant.IS_CLOSED);
            return payOutComeVo;
        }
    }


    /**
     * 更新支付订单信息
     *
     * @Param:@param record example
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/21
     */
    @Override
    public boolean updateEntity(WxPayOrder record) {
        record.setCreateTime(new Date());
        return this.updateById(record);
    }

    /**
     * 插入支付订单信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/21
     */
    @Override
    public String insertEntity(WxPayOrderDTO record) {
        WxPayOrder entity = WxPayOrderConvert.INSTANCE.dtoToEntity(record);
        this.save(entity);
        return entity.getId().toString();
    }


    /**
     * 更新支付结果
     *
     * @param wxPayOrderNotifyResult
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/21 14:45
     */
    @Override
    public void updateOrderStatus(WxPayOrderNotifyResult wxPayOrderNotifyResult) {
        LambdaQueryWrapper<WxPayOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WxPayOrder::getOutTradeNo, wxPayOrderNotifyResult.getOutTradeNo());
        WxPayOrder one = this.getOne(lambdaQueryWrapper);
        if (null == one) {
            log.error("未找到该订单");
            return;
        }
        one.setTransactionId(wxPayOrderNotifyResult.getTransactionId());
        //one.setTimeEnd(wxPayOrderNotifyResult.getTimeEnd());
        one.setUpdateTime(new Date());
        one.setIsSubscribe(wxPayOrderNotifyResult.getIsSubscribe());
        if (wxPayOrderNotifyResult.getResultCode().equals("SUCCESS")) {
            one.setStatus(OrderStatusEnum.paid.getCode());
        }
        one.setOriginalMessage(JSONUtil.toJsonStr(wxPayOrderNotifyResult));
        this.updateById(one);
        //开通会员
        MemberEnum memberEnum = MemberEnum.valueOfKey(one.getGoodsId());
        wxUserInfoService.updateVipTime(one.getOpenid(), memberEnum.getMonth());
        log.info("支付结果:{}", wxPayOrderNotifyResult);
    }


    /**
     * 创建支付订单
     *
     * @param goodsId
     * @return WxPayUnifiedOrderRequest
     * @Author: zc.wu
     * @Date: 2023/4/21 14:16
     */
    @Override
    public WxPayOrderVO createWxOrderPay(WxPayUnifiedOrderRequest request, Integer goodsId) {
        //根据用户查询是否有未完成的订单 删除
        LambdaQueryWrapper<WxPayOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WxPayOrder::getOpenid, getUserId());
        lambdaQueryWrapper.eq(WxPayOrder::getStatus, OrderStatusEnum.unpaid.getCode());
        List<WxPayOrder> list = this.list(lambdaQueryWrapper);
        //删除未完成的订单
        list.forEach(item -> {
            this.removeById(item.getId());
        });
        WxPayOrder wxPayOrder = new WxPayOrder();
        BeanUtil.copyProperties(request, wxPayOrder);
        wxPayOrder.setGoodsId(goodsId);
        WxPayOrderVO wxPayOrderVO = WxPayOrderConvert.INSTANCE.entityToVO(wxPayOrder);
        //创建支付订单
        wxPayOrder.setStatus(OrderStatusEnum.unpaid.getCode());
        wxPayOrder.setCreateTime(new Date());
        this.save(wxPayOrder);
        return wxPayOrderVO;
    }


    /**
     * 创建支付订单
     *
     * @param goodsId
     * @return WxPayUnifiedOrderRequest
     * @Author: zc.wu
     * @Date: 2023/4/21 14:16
     */
    @Override
    public Map<String, Object> createDyOrderPay(Integer goodsId) {
        MemberEnum memberEnum = MemberEnum.valueOfKey(goodsId);
        String orderNo = RandomUtil.randomString(40);
        Map<String, Object> requestObject = new HashMap<>();
        requestObject.put("appid", "tt46bc3001d98088b001");
        requestObject.put("out_order_no", orderNo);
        requestObject.put("total_amount", memberEnum.getPrice() * 100);
        requestObject.put("subject", "会员充值" + memberEnum.getTitle());
        requestObject.put("body", "会员充值" + memberEnum.getTitle());
        requestObject.put("valid_time", 900);
        requestObject.put("notify_url", "https://openai.alwzc.com/api/pay/dy/notify");
        String s = RestTemplateUtil.requestSign(requestObject);
        requestObject.put("sign", s);
        //根据用户查询是否有未完成的订单 删除
        LambdaQueryWrapper<WxPayOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WxPayOrder::getOpenid, getUserId());
        lambdaQueryWrapper.eq(WxPayOrder::getStatus, OrderStatusEnum.unpaid.getCode());
        List<WxPayOrder> list = this.list(lambdaQueryWrapper);
        //删除未完成的订单
        list.forEach(item -> {
            this.removeById(item.getId());
        });
        WxPayOrder wxPayOrder = new WxPayOrder();
        wxPayOrder.setBody(requestObject.get("body").toString());
        wxPayOrder.setOutTradeNo(orderNo);
        wxPayOrder.setTotalFee((double) (memberEnum.getPrice() * 100));
        wxPayOrder.setSpbillCreateIp(WebUtil.getIp());
        wxPayOrder.setNotifyUrl(requestObject.get("notify_url").toString());
        wxPayOrder.setTradeType("DOUYIN");
        wxPayOrder.setOpenid(getUserId());
        wxPayOrder.setOriginalMessage("");
        wxPayOrder.setCreateTime(new Date());
        wxPayOrder.setGoodsId(goodsId);
        WxPayOrderVO wxPayOrderVO = WxPayOrderConvert.INSTANCE.entityToVO(wxPayOrder);
        //创建支付订单
        wxPayOrder.setStatus(OrderStatusEnum.unpaid.getCode());
        wxPayOrder.setCreateTime(new Date());
        this.save(wxPayOrder);
        return requestObject;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxPayCodeVO createOrder(WxPayOrderDTO wxPayOrderDTO) throws WxPayException {
        final String timestamp = String.valueOf(System.currentTimeMillis());
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        wxPayOrderDTO.setTradeType("NATIVE");
        Date newDate = DateUtils.getDateAfter(new Date(), Calendar.MINUTE, 5);
        final Integer currentLoginId;
        if(wxPayOrderDTO.getUserId() == null){
            currentLoginId =CurrentUserUtil.getV2UserId();
        }else{
            currentLoginId = wxPayOrderDTO.getUserId();
        }


        //锁前缀
        final String lockPrefix = "ORDER_USER" + currentLoginId;
        //上锁
        final boolean lock = lockHelper.lock(lockPrefix, timestamp);

        try {
            if (!lock) {
                throw new ServiceException("请勿重复下单");
            }

            final String key = OrderConstant.ORDER_PAY+ ApplicationConstant.CHANNEL_WX+"-"+wxPayOrderDTO.getTradeType()+"-" + currentLoginId + wxPayOrderDTO.getGoodsId();
            if (redisService.exists(key)) {
                //生成BASE64图片给前端
                return (WxPayCodeVO) redisService.get(key);
            }

            final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                    .lambda()
                    .eq(Product::getProductId, wxPayOrderDTO.getGoodsId())
                    .le(Product::getStartTime,format)
                    .gt(Product::getEndTime,format)
                    .eq(Product::getStatus,"0")
                    .select(Product::getProductId, Product::getProductPrice, Product::getNum,Product::getType,
                            Product::getUnit, Product::getProductName, Product::getPackageInfo,Product::getRemark)
            );
            if (product == null) {
                throw new ServiceException("商品不存在");
            }
            Resource resource = new ClassPathResource("img/appwxLogo.png");
            BufferedImage logoImage = ImageIO.read(resource.getStream());
            final String orderNo = idGeneratorUtils.getOrderNo();

            wxPayOrderDTO.setOutTradeNo(orderNo);

            wxPayOrderDTO.setExpiresTime(newDate);

            getWxPayOrderDTO(wxPayOrderDTO,product);
            //wxPayCodeVO.setCreatedTime(now);
            return getWxPayCodeVO(wxPayOrderDTO,product,newDate,orderNo,logoImage,key);
        }catch (IOException  | WriterException e) {
            log.error("生成支付二维码失败:{}",e.getMessage());
            throw new ServiceException("生成支付二维码失败");
        } finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxPayCodeVO createPrepayOrder(WxPayOrderDTO wxPayOrderDTO) {
        final String timestamp = String.valueOf(System.currentTimeMillis());
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

        Date newDate = DateUtils.getDateAfter(new Date(), Calendar.MINUTE, 5);

        final Integer currentLoginId;
        if(wxPayOrderDTO.getUserId() == null){
            currentLoginId =CurrentUserUtil.getV2UserId();
        }else{
            currentLoginId = wxPayOrderDTO.getUserId();
        }

        //锁前缀
        final String lockPrefix = "ORDER_USER" + currentLoginId;
        //上锁
        final boolean lock = lockHelper.lock(lockPrefix, timestamp);

        try {
            if (!lock) {
                throw new ServiceException("请勿重复下单");
            }
            final String key = OrderConstant.ORDER_PAY+ApplicationConstant.CHANNEL_WX+"-"+wxPayOrderDTO.getTradeType()+"-" + currentLoginId + wxPayOrderDTO.getGoodsId();
            if (redisService.exists(key)) {
                //生成BASE64图片给前端
                return (WxPayCodeVO) redisService.get(key);
            }
            final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                    .lambda()
                    .eq(Product::getProductId, wxPayOrderDTO.getGoodsId())
                    .le(Product::getStartTime,format)
                    .gt(Product::getEndTime,format)
                    .eq(Product::getStatus,"0")
                    .select(Product::getProductId, Product::getProductPrice, Product::getNum,Product::getType,
                            Product::getUnit, Product::getProductName, Product::getPackageInfo,Product::getRemark)
            );
            if (product == null) {
                throw new ServiceException("商品不存在");
            }

            Resource resource = new ClassPathResource("img/appwxLogo.png");
            BufferedImage logoImage = ImageIO.read(resource.getStream());
            final String orderNo = idGeneratorUtils.getOrderNo();
            wxPayOrderDTO.setOutTradeNo(orderNo);
            wxPayOrderDTO.setExpiresTime(newDate);
            getWxPayOrderDTO(wxPayOrderDTO,product);

            return getWxPayCodeVO(wxPayOrderDTO,product,newDate,orderNo,logoImage,key);
        }catch (Exception e) {
            log.error("预订单生成失败:{}",e.getMessage());
            throw new ServiceException("预订单生成失败");
        } finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }

    }


    @Override
    public boolean createTransfer(WxPayOrderDTO wxPayOrderDTO) {

        return false;
    }

    private void getWxPayOrderDTO(WxPayOrderDTO wxPayOrderDTO,Product product) {

        wxPayOrderDTO.setSpbillCreateIp(WebUtil.getIp());
        if("TAROT".equals(product.getType())){
            wxPayOrderDTO.setBody(product.getRemark());
        }else{
            wxPayOrderDTO.setBody(product.getProductName());
        }

        wxPayOrderDTO.setTotalFee(product.getProductPrice());
        wxPayOrderDTO.setNotifyUrl(notifyUrl);
        wxPayOrderDTO.setNum(product.getNum());
        wxPayOrderDTO.setUnit(product.getUnit());
        wxPayOrderDTO.setProductType(product.getType());
        if(wxPayOrderDTO.getUserId() == null){
            wxPayOrderDTO.setUserId(CurrentUserUtil.getV2UserId());
        }

        LocalDateTime now = LocalDateTime.now();
        //LocalDateTime 减8小时
        //wxPayOrderDTO.setCreateTime(now.minusHours(8));

        wxPayOrderDTO.setCreateTime(now.minusHours(8));
        //wxPayOrderDTO.setCreateTime(now);
        this.insertEntity(wxPayOrderDTO);

        //return wxPayOrderDTO;
    }
    private WxPayCodeVO getWxPayCodeVO(WxPayOrderDTO wxPayOrderDTO,Product product,Date newDate,String orderNo,BufferedImage logoImage,String key) throws WxPayException, IOException, WriterException {

        WxPayUnifiedOrderV3Request wxOrderV3 = new WxPayUnifiedOrderV3Request();
        // 商品描述
        if("TAROT".equals(product.getType())){
            //wxPayOrderDTO.setBody(product.getRemark());
            wxOrderV3.setDescription(product.getRemark());
            wxService.switchover(WxMchIdEnum.tarot.getCode());
            log.info("微信支付.切换到tarot商户: {}", wxService.getConfig().getMchId());
        }else{
            wxOrderV3.setDescription(product.getProductName());
            wxService.switchover(WxMchIdEnum.zns.getCode());
            log.info("微信支付.切换到zns商户: {}", wxService.getConfig().getMchId());
        }

        //商户订单号
        wxOrderV3.setOutTradeNo(orderNo);


        String format2 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX").format(newDate);

        wxOrderV3.setTimeExpire(format2);

        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setCurrency("CNY");
        BigDecimal num = new BigDecimal(product.getProductPrice()+"");
        BigDecimal result = num.multiply(new BigDecimal("100"));
        amount.setTotal(result.toBigInteger().intValue());
        wxOrderV3.setAmount(amount);

        Object orderV3 ;
        WxPayCodeVO wxPayCodeVO = new WxPayCodeVO();
        switch (wxPayOrderDTO.getTradeType()){
            case "JSAPI":
                if(ObjectUtil.isNotEmpty(wxPayOrderDTO.getAppId())){
                        wxOrderV3.setAppid(wxPayOrderDTO.getAppId());
                }
                wxOrderV3.setPayer(new WxPayUnifiedOrderV3Request.Payer().setOpenid(wxPayOrderDTO.getOpenid()));
                orderV3 = wxService.createOrderV3(TradeTypeEnum.JSAPI, wxOrderV3);

                wxPayCodeVO.setPrepayId(orderV3);
                break;
            case "H5":
                WxPayUnifiedOrderV3Request.SceneInfo sceneInfo = new WxPayUnifiedOrderV3Request.SceneInfo();
                sceneInfo.setPayerClientIp(wxPayOrderDTO.getSpbillCreateIp());
                sceneInfo.setH5Info(new WxPayUnifiedOrderV3Request.H5Info().setType(wxPayOrderDTO.getType()));
                wxOrderV3.setSceneInfo(sceneInfo);
                orderV3 = wxService.createOrderV3(TradeTypeEnum.H5, wxOrderV3);
                wxPayCodeVO.setH5Url(orderV3.toString());
                break;
            case "NATIVE":
                orderV3 = wxService.createOrderV3(TradeTypeEnum.NATIVE, wxOrderV3);
                String bytes= QRCodeGenerator.generateQRCode(orderV3.toString(), logoImage);
                wxPayCodeVO.setQrCode(bytes);
                break;
            default:
                throw new ServiceException("支付方式错误");
        }

        wxPayCodeVO.setOrdersId(orderNo);
        wxPayCodeVO.setProductName(product.getProductName());
        wxPayCodeVO.setProductType(product.getType());
        wxPayCodeVO.setProductPrice(product.getProductPrice());
        redisService.set(key, wxPayCodeVO, 5 * 60L);
        //LocalDateTime 加8小时

        wxPayCodeVO.setCreatedTime(wxPayOrderDTO.getCreateTime().plusHours(8));

        ///unpaidOrderQueue.addOrder(orderNo);


        return wxPayCodeVO;
        //return wxPayOrderDTO;
    }


}
