package com.hncboy.chatgpt.front.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.ChannelConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import com.hncboy.chatgpt.front.mapper.ChannelConfigMapper;
import com.hncboy.chatgpt.front.mapper.SiteInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

import static cn.hutool.extra.spring.SpringUtil.getActiveProfile;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.STATUS_DISABLE;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.STATUS_ENABLE;
import static com.hncboy.chatgpt.front.framework.util.DingTalkHookUtil.sendDingTalk;

/**
 * @Description: 渠道配置查询
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2024/3/27
 */
@Component
@RequiredArgsConstructor
public class ChannelConfigHelper {

    private final ChannelConfigMapper channelConfigMapper;
    private final SiteInfoMapper siteInfoMapper;
    private final ChatConfig chatConfig;

    public SiteInfo queryChannelInfoByGid(String modelGid) {
        List<ChannelConfig> channelConfigs = new ArrayList<>();
        if (modelGid.startsWith("gpt-4-gizmo")) {
            channelConfigs = channelConfigMapper.selectChannelConfigWithSiteInfo(modelGid);
            if (CollUtil.isEmpty(channelConfigs)) {
                modelGid = "gpt-4-gizmo";
            }
        } else if (modelGid.startsWith("app-")){
            modelGid = "dify-app";
        }
        if (CollUtil.isEmpty(channelConfigs)) {
            channelConfigs = channelConfigMapper.selectChannelConfigWithSiteInfo(modelGid);
        }
        if (CollUtil.isEmpty(channelConfigs)) {
            SiteInfo siteInfo = new SiteInfo();
            siteInfo.setName("默认站点");
            siteInfo.setUrl(chatConfig.getOpenaiApiBaseUrl());
            siteInfo.setApiKey(chatConfig.getOpenaiApiKey());
            return siteInfo;
        }
        ChannelConfig channelConfig = channelConfigs.stream().findFirst().get();
        Integer status = channelConfig.getStatus();
        if (STATUS_DISABLE.equals(status)) {
            // 把所有站点都设置为可用状态
            channelConfigMapper.updateStatusByModelGid(channelConfig.getModelGid());
            sendDingTalk("模型【" + channelConfig.getModelGid() + "】的所有通道均不可用，重置所有通道状态！");
            // 获取优先级最高的站点
            channelConfig = channelConfigs.stream().min(Comparator.comparingInt(ChannelConfig::getPriority)).get();
        }
        //查询站点配置信息
        LambdaQueryWrapper<SiteInfo> siteInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        siteInfoLambdaQueryWrapper.eq(SiteInfo::getStatus, STATUS_ENABLE);
        siteInfoLambdaQueryWrapper.eq(SiteInfo::getId, channelConfig.getSiteId());
        List<SiteInfo> siteInfos = siteInfoMapper.selectList(siteInfoLambdaQueryWrapper);
        SiteInfo siteInfo = siteInfos.stream().findFirst().get();
        siteInfo.setChannelConfigStatus(channelConfig.getStatus());
        siteInfo.setChannelConfigId(channelConfig.getId());
        return siteInfo;
    }

    public void updateChannelStatusById(Integer channelConfigId, Integer status) {
        ChannelConfig channelConfig = channelConfigMapper.selectById(channelConfigId);
        if (channelConfig == null) {
            return;
        }
        channelConfig.setStatus(status);
        if (STATUS_DISABLE.equals(status)) {
            channelConfig.setFailedTimes(channelConfig.getFailedTimes() + 1);
        }

        channelConfigMapper.updateById(channelConfig);
    }

    public void updateChannelStatusById2(Integer channelConfigId, Integer status) {
        ChannelConfig channelConfig = channelConfigMapper.selectById(channelConfigId);
        if (channelConfig == null) {
            return;
        }
        channelConfig.setStatus(status);
        if (STATUS_DISABLE.equals(status)) {
            channelConfig.setFailedTimes(channelConfig.getFailedTimes() + 1);
        }

        //发送钉钉消息
        // 构建消息内容
        String message = "=== ChatOI通道异常 ===\n" + "time:\t" + DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss") + "\n"
                + "env:\t\t" + getActiveProfile()+ "\n"
                + "model_gid:\t" + channelConfig.getModelGid() + "\n"
                + "site_name:\t" + channelConfig.getSiteName() + "\n"
                + "key_name:\t" + Optional.ofNullable(channelConfig.getApiKeyName()).orElse("") + "\n"
                + "ch_config_id:\t" + channelConfig.getId() + "\n"
                + "=== END ===";
        sendDingTalk(message);



        channelConfigMapper.updateById(channelConfig);
    }



}
