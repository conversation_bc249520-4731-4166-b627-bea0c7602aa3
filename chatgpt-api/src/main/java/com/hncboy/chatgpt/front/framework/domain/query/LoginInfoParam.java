package com.hncboy.chatgpt.front.framework.domain.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class LoginInfoParam implements Serializable {

    private static final long serialVersionUID = 2848911593389853617L;

    @NotBlank(message = "账户、手机号不能为空")
    @Schema(title = "账户、手机号")
    private String account;

    @NotBlank(message = "验证码不能为空")
    @Schema(title = "验证码")
    private String verificationCode;

    @Schema(title = "微信二维码凭证")
    private String ticket;

    @Schema(title = "openId")
    private String openId;

    @Schema(title = "nickName")
    private String nickName;

    @Schema(title = "headImgUrl")
    private String headImgUrl;

    @NotNull(message = "登录方式不能为空")
    @Schema(title = "登录方式0账户1手机")
    private Integer logonType;

    @Schema(title = "手机短信或图片CODE,发送短信后产生")
    private String idCode;

    @Schema(title = "上级ID")
    private String parentId;

    @Schema(title = "邮箱")
    private String email;

    @Schema(title = "用户类型")
    private String userType;
    @Schema(title = "IP地址")
    private String ipAddress;


}
