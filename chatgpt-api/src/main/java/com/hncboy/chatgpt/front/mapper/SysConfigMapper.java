package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.SysConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 系统配置相关信息 Mapper
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    @Select("select config_value from sys_config where config_key=#{key}")
    String getValue(String key);
}
