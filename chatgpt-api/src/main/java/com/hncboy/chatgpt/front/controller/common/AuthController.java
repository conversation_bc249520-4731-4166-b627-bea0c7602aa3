package com.hncboy.chatgpt.front.controller.common;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.hncboy.chatgpt.front.framework.domain.dto.PrompterInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.vo.HomeConfigVO;
import com.hncboy.chatgpt.front.framework.domain.vo.PrompterInfoVO;
import com.hncboy.chatgpt.front.framework.enums.MemberEnum;
import com.hncboy.chatgpt.front.framework.enums.WxMchIdEnum;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.SignInUtils;
import com.hncboy.chatgpt.front.framework.util.WebUtil;
import com.hncboy.chatgpt.front.handler.mp.handler.MsgHandler2;
import com.hncboy.chatgpt.front.helper.ChatMsgBuildHelper;
import com.hncboy.chatgpt.front.helper.HttpUrlTransferHelper;
import com.hncboy.chatgpt.front.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/22 19:48 鉴权相关接口
 */
@AllArgsConstructor
@Tag(name = "鉴权相关接口")
@RestController
@RequestMapping
@Slf4j
public class AuthController {


    private final HomeConfigService homeConfigService;
    private final PrompterInfoService prompterInfoService;
    private final UserConfigService userConfigService;
    private final WxUserInfoService wxUserInfoService;
    private final WxMpService wxMpService;
    private final PayService payService;
    private final ChatMsgBuildHelper chatMsgBuildHelper;
    private final HttpUrlTransferHelper httpUrlTransferHelper;
    private final MsgHandler2 msgHandler2;

    @Operation(summary = "模糊匹配下拉框")
    @PostMapping("/keyWord")
    public R<Object> queryListByKey(@RequestBody PrompterInfoDTO dto) {
        if (StringUtils.isEmpty(dto.getKeyWord()) || dto.getKeyWord().length() < 2) {
            return R.data(null);
        }
        IPage<PrompterInfoVO> prompterInfoVOS = prompterInfoService.queryListEntityPage(dto);
        return R.data(prompterInfoVOS);
    }


    @Operation(summary = "获取提词器")
    @GetMapping("/prompt")
    public R<String> prompt() {
        String str = ResourceUtil.readUtf8Str("classpath:data/tcq.txt");
        JSONArray objects = JSONUtil.parseArray(JSONUtil.toJsonStr(str));
        JSONArray objects1 = SignInUtils.GetRandomThreeInfoList(objects, 10);
        return R.data(objects1.toString());
    }


    @Operation(summary = "随机查询四条新闻展示在首页")
    @GetMapping("/queryNews")
    public R<List<String>> queryNews() {
        return R.data(httpUrlTransferHelper.queryNews());
    }



    @Operation(summary = "配置列表")
    @GetMapping("/config")
    public R<Object> queryAllList(String code, String roleName) {
        HomeConfigVO homeConfigVO = new HomeConfigVO();
        homeConfigVO.setOpenId(CurrentUserUtil.checkUserLogin());
        homeConfigVO.setCode(code);
        homeConfigVO.setTitle(roleName);
        List<HomeConfigVO> homeConfigs = homeConfigService.queryAllList(homeConfigVO);
        return R.data(homeConfigs);
    }


    @Operation(summary = "查询系统房间")
    @GetMapping("/querySystemRoom")
    public R<List<HomeConfigVO>> querySystemRoom() {
        HomeConfigVO homeConfigVO = new HomeConfigVO();
        homeConfigVO.setStatus(1);
        List<HomeConfigVO> homeConfigs = homeConfigService.queryAllList(homeConfigVO);
        return R.data(homeConfigs);
    }


    @Operation(summary = "会员列表")
    @GetMapping("/memberList")
    public R<Object> queryAllList() {
        List<Map<String, Object>> list = MemberEnum.toList();
        return R.data(list);
    }


    @Operation(summary = "查询系统配置")
    @GetMapping("/sysConfig")
    public R<Object> querySysConfig() {
        return R.data(userConfigService.querySysConfig());
    }


    @Operation(summary = "根据用户生成推广二维码")
    @GetMapping("/createImage")
    public R<Object> createImage(String openId) throws WxErrorException {
        Integer res = wxUserInfoService.createMpQrCodeTicket(openId);
        String s = "";
        if (Objects.nonNull(res)) {
            WxMpQrCodeTicket wxMpQrCodeTicket = wxMpService.getQrcodeService().qrCodeCreateLastTicket(res);
            s = wxMpService.getQrcodeService().qrCodePictureUrl(wxMpQrCodeTicket.getTicket());
        }
        return R.data(s);
    }


    @Operation(summary = "外部调用接口")
    @PostMapping("/externalCallChat")
    public R<String> externalCallChat(@RequestBody ChatProcessRequest chatProcessRequest, HttpServletRequest request) {
        String ip = WebUtil.getIp();
        log.info("请求IP,{}", ip);
        if (!ip.equals("************")) {
            return R.fail("请求不合法");
        }
        return R.data(chatMsgBuildHelper.buildApiMsg(chatProcessRequest));
    }

    /**
     * 支付宝授权
     *
     * @param request the request
     * @return the string
     */
    @PostMapping(value = "/callback/order", name = "支付宝授权回调地址", produces = MediaType.APPLICATION_JSON_VALUE)
    public String alipayPullback(HttpServletRequest request) {
        return payService.alipayPullback(request);
    }

    @Operation(summary = "微信支付回调通知处理")
    @PostMapping("/notify/order/{type}")
    public String parseOrderNotifyResult(@RequestBody Map body,HttpServletRequest request, @PathVariable final String type) {
        log.info("支付回调：{}", body);
        try {
            String mchId = WxMchIdEnum.valueOf(type).getCode();
            request.setAttribute("mchId", mchId);
            log.info("微信支付回调.切换到{}商户: {}", type, mchId);
            payService.wxPullback(body,request);
        }catch (Exception e){
           return WxPayNotifyResponse.fail("失败");
        }

        return WxPayNotifyResponse.success("成功");
    }


    //模拟微信公众号回调
    @GetMapping("/test/bestow/callback")
    public String testCallback(String openId) {
        return msgHandler2.handelAddTarotCoins(openId);
    }
}
