package com.hncboy.chatgpt.front.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JsonFixer {

    public static void main(String[] args) {
        // 示例格式错误的JSON，包括缺失label、description、key和value
        String malformedJson = "[{\"label\": \"    整体感受\", \"description\": \"<解读内容>    \"}, " +
                "{\"description\": \"皇后牌代表温柔\\n关怀。\\u00" +
                "3c解读\\u003e\", \"card_name\": \"皇后   正位\"}, " +
                "{\"la bel\": \"第二张牌\", \"card_" +
                "name\": \"国王正位\", \"invalid\":}, " +
                "{:\"invalid_value\", \"label\": \"第三 张牌\"}]";

        // 修复JSON
        String fixedJson = JsonFixer.fixJson(malformedJson);
        System.out.println("修复后的JSON: " + fixedJson);

        // 解析为List<Map>
        List<Map<String, String>> list = JsonFixer.parseToList(fixedJson);
        System.out.println("解析结果: " + list);

        // 转换为标准JSON
        String finalJson = JsonFixer.toJsonString(list);
        System.out.println("最终JSON: " + finalJson);
    }

    /**
     * 修复格式错误的JSON字符串，返回标准JSON字符串。
     *
     * @param jsonString 可能格式错误的JSON字符串
     * @return 修复后的标准JSON字符串
     */
    public static String fixJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return "[]";
        }

        // 步骤1：预处理
        String processed = preprocess(jsonString);
        System.out.println("预处理后的结果：" + processed);

        // 步骤2：结构修复
        String repaired = repairStructure(processed);
        System.out.println("结构修复后的结果：" + repaired);

        return repaired;
    }

    /**
     * 解析修复后的JSON字符串为List<Map<String, String>>，补全缺失的label和description。
     *
     * @param jsonString 修复后的JSON字符串
     * @return 符合格式的List<Map<String, String>>
     */
    public static List<Map<String, String>> parseToList(String jsonString) {
        List<Map<String, String>> result = new ArrayList<>();
        String cleaned = jsonString.trim();

        // 确保是数组
        if (!cleaned.startsWith("[") || !cleaned.endsWith("]")) {
            return result;
        }

        // 移除外层方括号
        cleaned = cleaned.substring(1, cleaned.length() - 1).trim();
        if (cleaned.isEmpty()) {
            return result;
        }

        // 分割对象
        List<String> objects = splitObjects(cleaned);
        for (String objStr : objects) {
            System.out.println("-----------对象分割----------: "+objStr);
            Map<String, String> map = parseObject(objStr);
            // 补全缺失的label和description
            if (!map.containsKey("label")) {
                map.put("label", "未知标签");
            }
            if (!map.containsKey("description")) {
                map.put("description", "无描述");
            }
            result.add(map);
        }

        return result;
    }

    /**
     * 将List<Map<String, String>>转换为标准JSON字符串。
     *
     * @param list 解析后的对象列表
     * @return 标准JSON字符串
     */
    public static String toJsonString(List<Map<String, String>> list) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> map = list.get(i);
            sb.append("{");
            int j = 0;
            for (Map.Entry<String, String> entry : map.entrySet()) {
                sb.append("\"").append(escapeString(entry.getKey())).append("\":\"")
                        .append(escapeString(entry.getValue())).append("\"");
                if (j < map.size() - 1) {
                    sb.append(",");
                }
                j++;
            }
            sb.append("}");
            if (i < list.size() - 1) {
                sb.append(",");
            }
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 预处理JSON字符串，处理Unicode、转义字符和无效键值对。
     */
    private static String preprocess(String input) {
        String result = input;

        // 处理多余转义字符
        result = result.replace("\\\\\"", "\\\"").replace("\\\\n", "\\n")
                .replace("\\\\r", "\\r").replace("\\\\t", "\\t");
        System.out.println("处理多余转义字符：" + result);

        // 处理Unicode转义序列
        Pattern unicodePattern = Pattern.compile("\\\\u([0-9a-fA-F]{4})");
        Matcher matcher = unicodePattern.matcher(result);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String unicode = matcher.group(1);
            char ch = (char) Integer.parseInt(unicode, 16);
            matcher.appendReplacement(sb, Matcher.quoteReplacement(String.valueOf(ch)));
        }
        matcher.appendTail(sb);
        result = sb.toString();
        System.out.println("处理Unicode转义序列: "+result);

        // 移除无效键值对（如:"value"或"key": ,）
        result = result.replaceAll("[:,]\\s*\"[^\"]*\"\\s*(?=[,\\}])", "");
        System.out.println("移除无效键值对: "+result);

        result = result.replaceAll("\"[^\"]*\"\\s*:\\s*(?=[,\\}])", "");
        System.out.println("移除无效键值对: "+result);

        return result;
    }

    /**
     * 修复JSON结构，添加缺失的括号，移除多余逗号。
     */
    private static String repairStructure(String input) {
        StringBuilder sb = new StringBuilder();
        int arrayDepth = 0;
        int objectDepth = 0;
        boolean inString = false;
        char stringQuote = 0;

        // 确保以数组开始
        if (!input.trim().startsWith("[")) {
            sb.append("[");
            arrayDepth++;
        } else {
            sb.append("[");
            arrayDepth++;
            input = input.trim().substring(1);
        }

        // 逐字符处理
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c == '"' && (i == 0 || input.charAt(i - 1) != '\\')) {
                inString = !inString;
                stringQuote = inString ? '"' : 0;
            }

            if (!inString) {
                if (c == '{') {
                    objectDepth++;
                } else if (c == '}') {
                    objectDepth--;
                } else if (c == '[') {
                    arrayDepth++;
                } else if (c == ']') {
                    arrayDepth--;
                }
            }

            sb.append(c);
        }

        // 添加缺失的闭合括号
        while (objectDepth > 0) {
            sb.append("}");
            objectDepth--;
        }
        while (arrayDepth > 0) {
            sb.append("]");
            arrayDepth--;
        }

        // 移除多余逗号
        String result = sb.toString();
        result = result.replaceAll(",\\s*]", "]").replaceAll(",\\s*}", "}").replaceAll(",\\s*,", ",");

        return result;
    }

    /**
     * 分割JSON数组中的对象，考虑嵌套结构。
     */
    private static List<String> splitObjects(String input) {
        List<String> objects = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        int objectDepth = 0;
        boolean inString = false;

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c == '"' && (i == 0 || input.charAt(i - 1) != '\\')) {
                inString = !inString;
            }

            if (!inString) {
                if (c == '{') {
                    objectDepth++;
                } else if (c == '}') {
                    objectDepth--;
                } else if (c == ',' && objectDepth == 0) {
                    objects.add(current.toString().trim());
                    current = new StringBuilder();
                    continue;
                }
            }
            current.append(c);
        }

        if (current.length() > 0) {
            objects.add(current.toString().trim());
        }

        return objects;
    }

    /**
     * 解析单个JSON对象为Map<String, String>，补全缺失的label和description。
     */
    private static Map<String, String> parseObject(String objStr) {
        Map<String, String> map = new HashMap<>();
        objStr = objStr.trim();
        if (!objStr.startsWith("{") || !objStr.endsWith("}")) {
            return map;
        }

        objStr = objStr.substring(1, objStr.length() - 1).trim();
        List<String> pairs = splitKeyValuePairs(objStr);
        for (String pair : pairs) {
            String[] parts = pair.split(":", 2);
            if (parts.length == 2 && !parts[0].trim().isEmpty() && !parts[1].trim().isEmpty()) {
                String key = unquote(parts[0].trim());
                String value = unquote(parts[1].trim());
                map.put(key, value);
            }
        }

        // 补全缺失的label和description
        if (!map.containsKey("label")) {
            map.put("label", "未知标签");
        }
        if (!map.containsKey("description")) {
            map.put("description", "无描述");
        }

        return map;
    }

    /**
     * 分割键值对，考虑嵌套对象。
     */
    private static List<String> splitKeyValuePairs(String input) {
        List<String> pairs = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        int objectDepth = 0;
        boolean inString = false;

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c == '"' && (i == 0 || input.charAt(i - 1) != '\\')) {
                inString = !inString;
            }

            if (!inString) {
                if (c == '{') {
                    objectDepth++;
                } else if (c == '}') {
                    objectDepth--;
                } else if (c == ',' && objectDepth == 0) {
                    String pair = current.toString().trim();
                    if (pair.contains(":")) {
                        pairs.add(pair);
                    }
                    current = new StringBuilder();
                    continue;
                }
            }
            current.append(c);
        }

        if (current.length() > 0) {
            String pair = current.toString().trim();
            if (pair.contains(":")) {
                pairs.add(pair);
            }
        }

        return pairs;
    }

    /**
     * 移除字符串两端的引号并处理转义。
     */
    private static String unquote(String str) {
        if (str.startsWith("\"") && str.endsWith("\"")) {
            str = str.substring(1, str.length() - 1);
        }
        return str.replace("\\\"", "\"").replace("\\n", "\n")
                .replace("\\r", "\r").replace("\\t", "\t");
    }

    /**
     * 转义字符串以符合JSON格式。
     */
    private static String escapeString(String str) {
        return str.replace("\"", "\\\"").replace("\n", "\\n")
                .replace("\r", "\\r").replace("\t", "\\t");
    }
}
