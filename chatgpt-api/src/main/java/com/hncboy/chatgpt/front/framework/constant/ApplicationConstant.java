package com.hncboy.chatgpt.front.framework.constant;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.RandomUtil;

/**
 * <AUTHOR>
 * @date 2023/3/27 21:41
 * 应用相关常量
 */
public interface ApplicationConstant {

    /**
     * ADMIN 路径前缀
     */
    String ADMIN_PATH_PREFIX = "admin";


    public final static String USER = "user";
    public final static String REDIS_USER_KEY = "user:";


    public final static String USER_NOW_COUNT = "USER_NOW_COUNT:";


    public final static String USER_IMAGE_COUNT = "USER_IMAGE_COUNT:";

    public static String sign_src = "https://alwzcoss.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/%E7%AD%BE%E5%88%B0.png";


    //五分钟的过期时间
    public static TimedCache<Integer, String> timedCache = CacheUtil.newTimedCache(300000);


    public final static String SMS_CODE_VALIDATE_KEY = "SMS_CODE_VALIDATE_KEY";


    public final static String SMS_PHONE_KEY = "SMS_PHONE_KEY";


    public final static String SMS_PHONE_NUM_KEY = "SMS_PHONE_NUM_KEY";


    public final static Integer CHECK_PHONE_NUM = 10;


    public final static Integer CHECK_IP_NUM = 20;

    public final static Integer NUMBER_ONE = 1;


    public static final String OK_CODE = "OK";


    public final static String SMS_IP_NUM_KEY = "SMS_IP_NUM_KEY";


    public final static Long TIME_OUT_60 = 60L;


    public final static Long TIME_OUT_300 = 300L;

    public final static Long TIME_OUT_900 = 900L;

    public final static Long TIME_OUT_HOUR = 3600L;

    public final static Integer STATUS_ENABLE = 0;
    public final static Integer STATUS_DISABLE = 1;

    public final static String TYPE_CHAT = "CHAT";
    public final static String TYPE_DRAW = "DRAW";
    public final static String TYPE_MUSIC = "MUSIC";
    public final static String TYPE_WRITE = "WRITE";
    public final static String TYPE_TAROT = "TAROT";

    public final static String MODEL_MUSIC = "SUNO";

    public  final static  String CHANNEL_ALIPAY = "alipay";
    public  final static  String CHANNEL_WX = "wx";

    public final  static  String ALIBABA_CLOUD_ACCESS_KEY_ID = "LTAI5t8yppg5scKKq4xrKkEF";
    public final  static  String ALIBABA_CLOUD_ACCESS_KEY_SECRET = "******************************";

    public final  static String endpoint = "mt.cn-hangzhou.aliyuncs.com";

    //场景值ID，临时二维码时为32位非0整型，永久二维码时最大值为100000（目前参数只支持1--100000
    public  final static  Integer WX_SCENE_ID = 10086;



    public  final static  String WX_TOCLET_URL = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=";


    public final static String appError = "不好意思，我有点忙，现在使用的人较多，您可以晚点使用，或者再尝试一次。";

    public  static String getUserHeaderImage (){
        int i = RandomUtil.randomInt(101, 129);
        return "https://image.zjfdsr.com/ai/avater_image_"+i+".png";
    };




    public static String imagePrompts = "根据用户给出的描述从以下方面进行优化\n" +
            "1、尽可能详细：指定绘画的元素、风格、色彩、背景等详细信息能帮助DALL-E3更准确地生成所要求的图像。\n" +
            "2、创新： 不限于常见的描述，可以尝试复杂、新颖的想法让DALL-E3创造出独特的图像。\n" +
            "3.逻辑连贯：虽然DALL-E3可以处理一些非常抽象和奇特的想法，但是让描述保持逻辑连贯，可以帮助DALL-E3更好的理解，并且生成质量更高、更符合预期的图像。\n" +
            "视角多样：尝试从不同的视角描述同一个事物，或者将不同的元素、场景、风格结合，可以增加生成图像的多样性和趣味性。\n" +
            "综合运用：根据需要，可以同时使用多个视角优化提示词，让DALL-E3生成更具有复杂性和深度的图像。\n" +
            "\n" +
            "根据以上这份描述自行判断风格是vivid还是natural，还有图片大小自行判断，可选项有1024x1024, 1792x1024, or 1024x1792，\n" +
            "最后根据以上要求组成一个json字符串给我，\n" +
            "{\n" +
            "\"size\"：\"图片大小\",\n" +
            "\"style\"：\"风格\"，\n" +
            "\"promptOld\"：\"优化后的描述\"，\n" +
            "\"promptNew\":\"优化后的描述请翻译成英文\"\n" +
            "}";


}
