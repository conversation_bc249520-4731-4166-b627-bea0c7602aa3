package com.hncboy.chatgpt.front.util;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.EscapeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import io.github.haibiiin.json.repair.JSONRepair;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;

@Slf4j
public class JsonRepair {
    private static boolean DEBUG = false;

    private static final String regex = "(\\*|_|`|#|\\+|-|\\*\\*|>|\\|\\||\\.\\.\\.|\\!|\\[|\\]|\\(|\\)|\\{|\\}|\\\\|\\^|\\~|\\\\\\*|\\\\\\+|\\\\\\-|\\\\\\[|\\\\\\]|\\\\\\(|\\\\\\)|\\\\\\{|\\\\\\}|\\\\\\\\|\\\\\\^|\\\\\\~|\\\\\\\\\\\\\\*|\\\\\\\\\\\\\\+|\\\\\\\\\\\\\\-|\\\\\\\\\\\\\\[|\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\(|\\\\\\\\\\\\\\)|\\\\\\\\\\\\\\{|\\\\\\\\\\\\\\}|\\\\\\\\\\\\\\\\\\\\\\\\\\\\*|\\\\\\\\\\\\\\\\\\\\\\\\\\\\+|\\\\\\\\\\\\\\\\\\\\\\\\\\\\-|\\\\\\\\\\\\\\\\\\\\\\\\\\\\[|\\\\\\\\\\\\\\\\\\\\\\\\\\\\]|\\\\\\\\\\\\\\\\\\\\\\\\\\\\(|\\\\\\\\\\\\\\\\\\\\\\\\\\\\)|\\\\\\\\\\\\\\\\\\\\\\\\\\\\*|\\\\\\\\\\\\\\\\\\\\\\\\\\\\}|~|\\\\~|\\\\\\\\\\\\\\~|\\\\\\\\\\\\\\\\\\\\\\\\\\\\~)+";

    private static final ObjectMapper OBJECT_MAPPER;

    private static final Gson GSON;

    private static final JSONRepair JSON_REPAIR;

    static {
        // 使用 Jackson 的宽松解析（Lenient 模式）
        // 支持无引号 key、单引号、尾逗号、注释、多余逗号、缺失值等
        JsonFactory f = JsonFactory.builder()
                .enable(JsonReadFeature.ALLOW_UNQUOTED_FIELD_NAMES)
                .enable(JsonReadFeature.ALLOW_SINGLE_QUOTES)
                .enable(JsonReadFeature.ALLOW_TRAILING_COMMA)
                .enable(JsonReadFeature.ALLOW_JAVA_COMMENTS)
                .enable(JsonReadFeature.ALLOW_MISSING_VALUES)
                .build();
        ObjectMapper mapper = JsonMapper.builder(f).build();
        mapper.findAndRegisterModules();
        OBJECT_MAPPER = mapper;

        //尝试 Gson 的宽松模式
        GSON = new GsonBuilder().setLenient().create();

        //Java 专门库，支持自动补全 []{}、quotes、commas 等常见错误；还可从长文本中抽取 JSON 并修复
        JSON_REPAIR = new JSONRepair();
    }

    public static void main(String[] args) {
        String text = null;

        //能修复 "card_name": \"星币五逆位\"
//        text = "{\"event\":\"message\",\"task_id\":\"8232bf7c-124a-41f6-8812-327f28883f01\",\"id\":\"51609671-80d0-4d1f-a2a2-cd9e5522fe4c\",\"message_id\":\"51609671-80d0-4d1f-a2a2-cd9e5522fe4c\",\"conversation_id\":\"31b960b2-7517-4aa4-8a0f-1bc2d87c84a2\",\"mode\":\"advanced-chat\",\"answer\":\"[\\n  {\\n    \\\"label\\\": \\\"整体感受\\\",\\n    \\\"description\\\": \\\"今天的运势整体上有点坎坷，感觉你可能会遇到一些物质或者精神上的压力，像是资源不足或者情绪低落的情况，心里可能会觉得有些被孤立或者不被支持。事情不太顺利，可能会让你有点疲惫和不安。不过呢，这种状况不代表会持续很久，更多是提醒你注意调整自己的状态和心态，别让自己陷得太深呢。\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"本张牌\\\",\\n    \\\"card_name\\\": \\\\\\\"星币五逆位\\\\\\\",\\n    \\\"description\\\": \\\"星币五逆位说明今天你可能会从之前的困境或匮乏中开始有转机。逆位的它不像正位那样彻底的失落和被抛弃，更多是提示你经历了难关后，正在慢慢走出阴霾。你可能感受到金钱或者资源压力减轻，也许有人愿意帮忙，或者你自己开始找到解决问题的办法。内心的孤单感会有所缓解，但还是需要谨慎，别急着做决定，给自己留点余地。\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"建议\\\",\\n    \\\"description\\\": \\\"今天要特别注意不要因为一时的困难就陷入负面情绪，星币五逆位告诉你，虽然状况不好，但已经在好转的路上。现实层面上，建议你理清手头的财务状况，优先处理最紧急的支出，避免冲动消费。工作上别急着做大动作，可以先稳住局面，慢慢扭转不利局面。情绪上多找信任的人聊聊，别自己硬抗。不需要马上解决所有问题，只要一步步走出阴霾就好呢。\\\"\\n  }\\n]\",\"metadata\":{\"usage\":{\"prompt_tokens\":2535,\"prompt_unit_price\":\"0\",\"prompt_price_unit\":\"0\",\"prompt_price\":\"0E-7\",\"completion_tokens\":1065,\"completion_unit_price\":\"0\",\"completion_price_unit\":\"0\",\"completion_price\":\"0E-7\",\"total_tokens\":3600,\"total_price\":\"0E-7\",\"currency\":\"USD\",\"latency\":3.883165393024683}},\"created_at\":1752393016}";

        //未修复 {    "建议",
//        text = "{\"event\":\"message\",\"task_id\":\"07959114-4b83-499e-99b2-78ef152d5dde\",\"id\":\"ac50bd02-9049-44e8-8cab-8dc5281b3d28\",\"message_id\":\"ac50bd02-9049-44e8-8cab-8dc5281b3d28\",\"conversation_id\":\"9debc0dc-f0a8-4c1c-a25d-6f36d4057d43\",\"mode\":\"advanced-chat\",\"answer\":\"[\\n  {\\n    \\\"label\\\": \\\"整体感受\\\",\\n    \\\"description\\\": \\\"从权杖二逆位来看，魏晓翔现在对你的感情并不是特别稳定或者明确。他并没有表现出强烈的积极行动或者明确的计划去维护或者推进你们之间的关系。相反，他可能在内心犹豫不决，缺乏主动，或者对未来的你们感到迷茫和不确定。综合来看，他现在并不是真的喜欢你，至少不是那种愿意主动去走下去的喜欢。\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"本张牌\\\",\\n    \\\"card_name\\\": \\\"权杖二逆位\\\",\\n    \\\"description\\\": \\\"权杖二正位代表着计划、远见和主动去开拓新的机会，但逆位则暗示行动上的停滞和迷茫。权杖牌属于火元素，与热情、动力和行动力紧密相关。这张牌逆位说明，在你问的‘魏晓翔还喜欢我吗’这个问题上，他缺乏对你们关系的清晰方向和推动力。可能他没有能力或意愿去规划未来，也没有表现出对这段感情的渴望和主动维护。简单来说，他现在可能在犹豫、退缩，或者不想面对这段关系的进展。\\\"\\n  },\\n  {\\n    \\\"建议\\\",\\n    \\\"description\\\": \\\"既然魏晓翔现在表现出的是不确定和停滞，你需要正视这段感情已经没有往前发展的动力。你可以把更多精力放在自己身上，比如提升自己、关注工作和生活的稳定。感情问题不能只靠等待和猜测，现实中你可以做的是保持自我价值，扩大自己的人际圈，给自己创造更多可能性。这样无论未来他是否回头，你都不会被困在感情的迷雾里，反而能够更好地吸引真正愿意为你付出的人。\\\"\\n  }\\n]\",\"metadata\":{\"usage\":{\"prompt_tokens\":2656,\"prompt_unit_price\":\"0\",\"prompt_price_unit\":\"0\",\"prompt_price\":\"0E-7\",\"completion_tokens\":1154,\"completion_unit_price\":\"0\",\"completion_price_unit\":\"0\",\"completion_price\":\"0E-7\",\"total_tokens\":3810,\"total_price\":\"0E-7\",\"currency\":\"USD\",\"latency\":4.152354707941413}},\"created_at\":1752492600}";

        //能修复 避免再出现找不到的情况。"]
//        text = "{\"event\":\"message\",\"task_id\":\"7b4e945a-f93f-43c0-abe2-b6e6fc6c3e03\",\"id\":\"36957ae2-9b82-40f9-86f6-2500b7a7d919\",\"message_id\":\"36957ae2-9b82-40f9-86f6-2500b7a7d919\",\"conversation_id\":\"661770dc-32d6-44ef-90aa-a85a30a8a78a\",\"mode\":\"advanced-chat\",\"answer\":\"[\\n  {\\n    \\\"label\\\": \\\"整体感受\\\",\\n    \\\"description\\\": \\\"星币侍从正位告诉我们，你的健身包很可能放在一个实际、稳妥且容易被忽视的地方。这张牌和土元素相关，代表着物质、现实和日常的管理，提示你需要从日常习惯和实地寻找，而不是想象中的复杂线索。你的健身包应该就在你平时最常使用或放置运动装备的地方，或者是你最近用来收纳小物件的空间里。\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"本张牌\\\",\\n    \\\"card_name\\\": \\\"星币侍从正位\\\",\\n    \\\"description\\\": \\\"星币侍从是一个年轻、细致且注重实际的人物。正位时，它象征着踏实、细心和对物质资源的管理。针对你的问题，这张牌代表你健身包的位置可能很接近你的日常活动区域，比如衣柜、车内的储物格，或者你平时准备运动装备的地方。它提醒你可以仔细检查一下最近你放置过东西的地方，尤其是那些你会用心整理的角落。星币元素的特性也暗示，这个包应该没有远离你的视线太远，而是在一个稳定且容易找到的空间。\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"建议\\\",\\n    \\\"description\\\": \\\"建议你回忆一下最近一次使用健身包的时间和地点，去你通常放置运动物品的地方仔细寻找，包括房间的角落、车里或甚至是健身包可能被暂时放置的包袋内。此外，可以检查一下你是否将它与其他物品混合放在一起，比如购物袋、备用包或者办公室的储物柜。星币侍从告诉你，找到它不需要复杂的线索，只要细心回想并检查你常用的具体位置就能找到。下次使用后，给它找个固定的归处，避免再出现找不到的情况。\\\"\\n]\",\"metadata\":{\"usage\":{\"prompt_tokens\":2537,\"prompt_unit_price\":\"0\",\"prompt_price_unit\":\"0\",\"prompt_price\":\"0E-7\",\"completion_tokens\":1182,\"completion_unit_price\":\"0\",\"completion_price_unit\":\"0\",\"completion_price\":\"0E-7\",\"total_tokens\":3719,\"total_price\":\"0E-7\",\"currency\":\"USD\",\"latency\":5.4870119243860245}},\"created_at\":1752605246}";

        //能修复 比单纯的行为更重要。\"\n]"
//        text = "{\"event\":\"message\",\"task_id\":\"12c8db15-262f-4bcb-8d85-19b4036b59d9\",\"id\":\"5fa5ee4b-d67a-482b-adcb-9c6c0800ebeb\",\"message_id\":\"5fa5ee4b-d67a-482b-adcb-9c6c0800ebeb\",\"conversation_id\":\"133b2e95-bd38-41eb-9f5e-bcffb1767375\",\"mode\":\"advanced-chat\",\"answer\":\"[\\n  {\\n    \\\"label\\\": \\\"整体感受\\\",\\n    \\\"description\\\": \\\"爷爷让你卸指甲这件事，表面看起来有点奇怪甚至让人不舒服，但其实背后隐藏着他内心的担忧和焦虑。宝剑九正位的出现，说明他可能在精神上承受着一定的压力，或者对某些事情感到非常担心。他让你卸指甲，不是随意的要求，而是一种象征性的行为，可能是在提醒你去除不必要的负担或戒除某些让他焦虑的事物。\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"本张牌\\\",\\n    \\\"card_name\\\": \\\"宝剑九正位\\\",\\n    \\\"description\\\": \\\"宝剑九正位代表着焦虑、担忧和内心的恐惧。爷爷可能正经历一些让他夜不能寐的事情，或者他的内心充满了不安和压力。卸指甲这个行为，象征着释放或清理那些让他不安的东西。指甲在生活中常象征保护和防御，卸掉指甲意味着去除一些防备，或是提醒你别给自己或者他制造更多的精神负担。这张牌显示爷爷的心里其实是很沉重的，他希望你能理解他这种复杂的情绪，或者通过这个行为来传达他内心的困扰。\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"建议\\\",\\n    \\\"description\\\": \\\"面对爷爷这样的行为，最好用温和和关心的方式去沟通，问问他是不是有什么特别的原因或者担忧。宝剑九告诉我们，焦虑和不安需要被倾听和缓解。你可以试着帮助爷爷减轻他的压力，比如多陪伴他，理解他的感受，或者帮助他去处理他担心的实际问题。切记不要直接拒绝或是强硬回应他的要求，反而会加重他的焦虑。通过耐心的对话和实际行动，让爷爷感受到安全和支持，比单纯的行为更重要。\\\"\\n]\",\"metadata\":{\"usage\":{\"prompt_tokens\":2329,\"prompt_unit_price\":\"0\",\"prompt_price_unit\":\"0\",\"prompt_price\":\"0E-7\",\"completion_tokens\":1181,\"completion_unit_price\":\"0\",\"completion_price_unit\":\"0\",\"completion_price\":\"0E-7\",\"total_tokens\":3510,\"total_price\":\"0E-7\",\"currency\":\"USD\",\"latency\":9.043741904199123}},\"created_at\":1752643095}";

        //能修复 \\u 系列正常文本
//        text = "{\"event\": \"message\", \"task_id\": \"48c17674-4ac4-41af-8150-08c628b3d21e\", \"id\": \"4c56b1e0-b533-45c6-aa11-1f3572f7b69e\", \"message_id\": \"4c56b1e0-b533-45c6-aa11-1f3572f7b69e\", \"conversation_id\": \"92ab7aff-011a-4762-aa7c-5156449fc3be\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u5f90\\u60e0\\u5b50\\u5bf9\\u9ec4\\u653f\\u8c6a\\u7684\\u5fc3\\u60c5\\u6709\\u4e9b\\u590d\\u6742\\u548c\\u4e0d\\u786e\\u5b9a\\u5462\\u3002\\u5979\\u6700\\u521d\\u5bf9\\u4ed6\\u7684\\u5370\\u8c61\\u5145\\u6ee1\\u4e86\\u795e\\u79d8\\u611f\\u548c\\u4e0d\\u5b89\\uff0c\\u65e5\\u5e38\\u76f8\\u5904\\u4e2d\\u611f\\u5230\\u4e00\\u79cd\\u539f\\u5219\\u548c\\u89c4\\u5219\\u7684\\u51b2\\u7a81\\uff0c\\u800c\\u5185\\u5fc3\\u6df1\\u5904\\uff0c\\u4f3c\\u4e4e\\u8fd8\\u6ca1\\u771f\\u6b63\\u5efa\\u7acb\\u8d77\\u7a33\\u5b9a\\u7684\\u559c\\u6b22\\u3002\\u6574\\u4f53\\u6765\\u770b\\uff0c\\u5979\\u5bf9\\u4ed6\\u7684\\u611f\\u60c5\\u8fd8\\u5728\\u6478\\u7d22\\u548c\\u8c03\\u6574\\u9636\\u6bb5\\uff0c\\u53ef\\u80fd\\u5fc3\\u91cc\\u6709\\u8bdd\\u60f3\\u8bf4\\u4f46\\u53c8\\u96be\\u4ee5\\u8868\\u8fbe\\u6e05\\u695a\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6708\\u4eae\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6708\\u4eae\\u6b63\\u4f4d\\u663e\\u793a\\u5f90\\u60e0\\u5b50\\u5728\\u521a\\u5f00\\u59cb\\u5bf9\\u9ec4\\u653f\\u8c6a\\u7684\\u5370\\u8c61\\u662f\\u6a21\\u7cca\\u4e14\\u5145\\u6ee1\\u4e0d\\u786e\\u5b9a\\u7684\\u3002\\u5979\\u53ef\\u80fd\\u89c9\\u5f97\\u4ed6\\u6709\\u4e00\\u4e9b\\u795e\\u79d8\\u611f\\uff0c\\u6216\\u8005\\u4ed6\\u7684\\u771f\\u5b9e\\u60f3\\u6cd5\\u548c\\u60c5\\u7eea\\u4e0d\\u592a\\u5bb9\\u6613\\u770b\\u900f\\u3002\\u5979\\u5185\\u5fc3\\u53ef\\u80fd\\u6709\\u4e9b\\u5bb3\\u6015\\u6216\\u62c5\\u5fe7\\uff0c\\u4e0d\\u786e\\u5b9a\\u8fd9\\u6bb5\\u5173\\u7cfb\\u80fd\\u5426\\u987a\\u5229\\u53d1\\u5c55\\u3002\\u5979\\u5bf9\\u4ed6\\u7684\\u8ba4\\u8bc6\\u8fd8\\u5904\\u5728\\u63a2\\u7d22\\u9636\\u6bb5\\uff0c\\u611f\\u53d7\\u5230\\u7684\\u662f\\u4e00\\u5c42\\u8ff7\\u96fe\\uff0c\\u9700\\u8981\\u65f6\\u95f4\\u53bb\\u7406\\u89e3\\u548c\\u63a5\\u7eb3\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6559\\u7687\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u5728\\u65e5\\u5e38\\u76f8\\u5904\\u548c\\u804a\\u5929\\u4e2d\\uff0c\\u6559\\u7687\\u9006\\u4f4d\\u8868\\u660e\\u5f90\\u60e0\\u5b50\\u89c9\\u5f97\\u8fd9\\u6bb5\\u5173\\u7cfb\\u4e2d\\u5b58\\u5728\\u4e00\\u4e9b\\u51b2\\u7a81\\u6216\\u4e0d\\u5408\\u62cd\\u3002\\u5979\\u53ef\\u80fd\\u89c9\\u5f97\\u9ec4\\u653f\\u8c6a\\u5728\\u67d0\\u4e9b\\u539f\\u5219\\u6216\\u4ef7\\u503c\\u89c2\\u4e0a\\u4e0e\\u81ea\\u5df1\\u6709\\u5206\\u6b67\\uff0c\\u6216\\u8005\\u611f\\u5230\\u4ed6\\u5e76\\u6ca1\\u6709\\u6309\\u7167\\u5979\\u671f\\u5f85\\u7684\\u65b9\\u5f0f\\u53bb\\u6c9f\\u901a\\u548c\\u4ea4\\u6d41\\u3002\\u8fd9\\u79cd\\u611f\\u89c9\\u8ba9\\u5979\\u89c9\\u5f97\\u5173\\u7cfb\\u7f3a\\u5c11\\u7a33\\u5b9a\\u6027\\uff0c\\u751a\\u81f3\\u6709\\u70b9\\u4e0d\\u88ab\\u7406\\u89e3\\u6216\\u88ab\\u675f\\u7f1a\\u7684\\u538b\\u529b\\u3002\\u4e0d\\u8fc7\\u8fd9\\u4e5f\\u662f\\u63d0\\u9192\\u5979\\uff0c\\u6216\\u8bb8\\u9700\\u8981\\u8c03\\u6574\\u5f7c\\u6b64\\u7684\\u76f8\\u5904\\u65b9\\u5f0f\\uff0c\\u627e\\u5230\\u66f4\\u9002\\u5408\\u7684\\u6c9f\\u901a\\u6a21\\u5f0f\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6743\\u6756\\u4e09\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6743\\u6756\\u4e09\\u9006\\u4f4d\\u663e\\u793a\\u5f90\\u60e0\\u5b50\\u5185\\u5fc3\\u5bf9\\u9ec4\\u653f\\u8c6a\\u7684\\u611f\\u60c5\\u8fd8\\u6ca1\\u6709\\u771f\\u6b63\\u7684\\u70ed\\u60c5\\u548c\\u671f\\u5f85\\u3002\\u5979\\u53ef\\u80fd\\u611f\\u5230\\u5bf9\\u672a\\u6765\\u7f3a\\u4e4f\\u4fe1\\u5fc3\\uff0c\\u6216\\u8005\\u8fd8\\u6ca1\\u51c6\\u5907\\u597d\\u5168\\u5fc3\\u6295\\u5165\\u8fd9\\u6bb5\\u5173\\u7cfb\\u3002\\u5979\\u559c\\u6b22\\u4ed6\\u7684\\u7a0b\\u5ea6\\u6bd4\\u8f83\\u4f4e\\uff0c\\u5fc3\\u91cc\\u6709\\u4e9b\\u72b9\\u8c6b\\u548c\\u4fdd\\u7559\\uff0c\\u4e5f\\u8bb8\\u662f\\u56e0\\u4e3a\\u524d\\u9762\\u63d0\\u5230\\u7684\\u77db\\u76fe\\u548c\\u4e0d\\u786e\\u5b9a\\u611f\\uff0c\\u8ba9\\u5979\\u96be\\u4ee5\\u5b8c\\u5168\\u653e\\u4e0b\\u6212\\u5907\\u5fc3\\u3002\\u8fd9\\u8bf4\\u660e\\u5979\\u8fd8\\u6ca1\\u786e\\u8ba4\\u81ea\\u5df1\\u5230\\u5e95\\u662f\\u5426\\u771f\\u5fc3\\u559c\\u6b22\\u4ed6\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u5f90\\u60e0\\u5b50\\u76ee\\u524d\\u5bf9\\u9ec4\\u653f\\u8c6a\\u7684\\u5fc3\\u610f\\u5e76\\u4e0d\\u660e\\u6717\\uff0c\\u521d\\u5370\\u8c61\\u5e26\\u7740\\u8ff7\\u832b\\u548c\\u4e0d\\u5b89\\uff0c\\u65e5\\u5e38\\u76f8\\u5904\\u4e2d\\u611f\\u53d7\\u5230\\u89c4\\u5219\\u548c\\u6c9f\\u901a\\u4e0a\\u7684\\u51b2\\u7a81\\uff0c\\u5185\\u5fc3\\u559c\\u6b22\\u7684\\u611f\\u89c9\\u4e5f\\u504f\\u5f31\\u3002\\u5979\\u53ef\\u80fd\\u6709\\u8bdd\\u60f3\\u8bf4\\u5374\\u96be\\u4ee5\\u8868\\u8fbe\\u6e05\\u695a\\uff0c\\u611f\\u60c5\\u5904\\u4e8e\\u8bd5\\u63a2\\u548c\\u8c03\\u6574\\u7684\\u9636\\u6bb5\\u3002\\u6574\\u4f53\\u6765\\u770b\\uff0c\\u5979\\u8fd8\\u6ca1\\u5b8c\\u5168\\u6253\\u5f00\\u81ea\\u5df1\\u53bb\\u63a5\\u53d7\\u8fd9\\u6bb5\\u5173\\u7cfb\\uff0c\\u4f46\\u4e5f\\u4e0d\\u662f\\u5b8c\\u5168\\u62d2\\u7edd\\uff0c\\u66f4\\u591a\\u7684\\u662f\\u5728\\u89c2\\u671b\\u548c\\u6743\\u8861\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u5efa\\u8bae\\u5f90\\u60e0\\u5b50\\u5982\\u679c\\u60f3\\u8ba9\\u8fd9\\u6bb5\\u5173\\u7cfb\\u66f4\\u987a\\u7545\\uff0c\\u53ef\\u4ee5\\u8bd5\\u7740\\u5148\\u5766\\u8bda\\u5730\\u8868\\u8fbe\\u81ea\\u5df1\\u7684\\u611f\\u53d7\\u548c\\u987e\\u8651\\uff0c\\u4e0d\\u8981\\u538b\\u6291\\u5185\\u5fc3\\u7684\\u4e0d\\u5b89\\u548c\\u7591\\u60d1\\u3002\\u53ef\\u4ee5\\u627e\\u4e00\\u4e2a\\u8f7b\\u677e\\u7684\\u65f6\\u673a\\uff0c\\u5206\\u4eab\\u81ea\\u5df1\\u7684\\u60f3\\u6cd5\\uff0c\\u907f\\u514d\\u8bef\\u4f1a\\u79ef\\u7d2f\\u3002\\u540c\\u65f6\\uff0c\\u4e5f\\u8981\\u591a\\u542c\\u542c\\u9ec4\\u653f\\u8c6a\\u7684\\u60f3\\u6cd5\\uff0c\\u770b\\u770b\\u6709\\u6ca1\\u6709\\u5f7c\\u6b64\\u53ef\\u4ee5\\u8c03\\u6574\\u7684\\u7a7a\\u95f4\\u3002\\u6c9f\\u901a\\u662f\\u6253\\u7834\\u8ff7\\u96fe\\u7684\\u5173\\u952e\\uff0c\\u6162\\u6162\\u5efa\\u7acb\\u4fe1\\u4efb\\u548c\\u7406\\u89e3\\u3002\\u4e0d\\u8981\\u6025\\u7740\\u7ed9\\u611f\\u60c5\\u4e0b\\u7ed3\\u8bba\\uff0c\\u7ed9\\u81ea\\u5df1\\u548c\\u5bf9\\u65b9\\u4e00\\u70b9\\u65f6\\u95f4\\u548c\\u8010\\u5fc3\\uff0c\\u611f\\u60c5\\u81ea\\u7136\\u4f1a\\u6709\\u7b54\\u6848\\u7684\\u5462\\u3002\\\"\\n  }\\n]\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3112, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 2017, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 5129, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 9.664318375987932}}, \"created_at\": 1753538475}";

        //未修复 ,\n    \"description\": \"徐惠子如果想让这段感"
//        text = "{\"event\": \"message\", \"task_id\": \"04fd8c0a-81f9-473e-9577-42ec098a801d\", \"id\": \"57e35860-743b-4d95-bfd5-13cdb99dd96a\", \"message_id\": \"57e35860-743b-4d95-bfd5-13cdb99dd96a\", \"conversation_id\": \"3e944801-6281-4d2c-9b7b-c9e6a71a8a25\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u5f90\\u60e0\\u5b50\\u5f53\\u4e0b\\u5bf9\\u9ec4\\u653f\\u8c6a\\u7684\\u5fc3\\u60c5\\u6709\\u4e9b\\u590d\\u6742\\uff0c\\u5185\\u5fc3\\u5145\\u6ee1\\u4e86\\u4e0d\\u786e\\u5b9a\\u548c\\u8ff7\\u832b\\u3002\\u4ed6\\u5bf9\\u8fd9\\u6bb5\\u5173\\u7cfb\\u7f3a\\u5c11\\u660e\\u786e\\u7684\\u4fe1\\u4efb\\u611f\\uff0c\\u4e5f\\u6ca1\\u6709\\u5b8c\\u5168\\u6253\\u5f00\\u5fc3\\u6249\\uff0c\\u751a\\u81f3\\u5e26\\u7740\\u4e00\\u4e9b\\u9632\\u5907\\u548c\\u56f0\\u60d1\\u5462\\u3002\\u6574\\u4f53\\u6765\\u8bf4\\uff0c\\u5979\\u73b0\\u5728\\u8fd8\\u6ca1\\u6709\\u5b8c\\u5168\\u786e\\u5b9a\\u81ea\\u5df1\\u7684\\u611f\\u60c5\\u65b9\\u5411\\uff0c\\u5bf9\\u4ed6\\u662f\\u559c\\u6b22\\u8fd8\\u53ea\\u662f\\u5b58\\u6709\\u7591\\u8651\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6708\\u4eae\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6708\\u4eae\\u6b63\\u4f4d\\u4ee3\\u8868\\u7684\\u662f\\u6726\\u80e7\\u548c\\u4e0d\\u786e\\u5b9a\\u7684\\u521d\\u5370\\u8c61\\u3002\\u5f90\\u60e0\\u5b50\\u5728\\u9ec4\\u653f\\u8c6a\\u5fc3\\u4e2d\\uff0c\\u521a\\u5f00\\u59cb\\u63a5\\u89e6\\u65f6\\u53ef\\u80fd\\u89c9\\u5f97\\u4ed6\\u795e\\u79d8\\u3001\\u6709\\u70b9\\u96be\\u4ee5\\u6349\\u6478\\uff0c\\u5979\\u770b\\u5230\\u7684\\u662f\\u4e00\\u79cd\\u590d\\u6742\\u4e14\\u6df1\\u6c89\\u7684\\u6c14\\u8d28\\uff0c\\u4f46\\u4e5f\\u4f34\\u968f\\u7740\\u5185\\u5fc3\\u7684\\u7591\\u60d1\\u548c\\u8ff7\\u832b\\u3002\\u5979\\u53ef\\u80fd\\u89c9\\u5f97\\u8fd9\\u6bb5\\u5173\\u7cfb\\u5f88\\u96be\\u4e00\\u4e0b\\u5b50\\u770b\\u6e05\\u695a\\u5168\\u8c8c\\uff0c\\u6709\\u70b9\\u50cf\\u8d70\\u5728\\u8ff7\\u96fe\\u4e2d\\uff0c\\u65e2\\u597d\\u5947\\u53c8\\u6709\\u70b9\\u4e0d\\u5b89\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6559\\u7687\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6559\\u7687\\u9006\\u4f4d\\u8bf4\\u660e\\u5728\\u65e5\\u5e38\\u76f8\\u5904\\u548c\\u804a\\u5929\\u4e2d\\uff0c\\u9ec4\\u653f\\u8c6a\\u611f\\u53d7\\u5230\\u5f90\\u60e0\\u5b50\\u6709\\u4e9b\\u53cd\\u4f20\\u7edf\\u6216\\u8005\\u4e0d\\u613f\\u610f\\u88ab\\u56fa\\u5b9a\\u5728\\u67d0\\u79cd\\u6846\\u67b6\\u91cc\\u3002\\u5979\\u53ef\\u80fd\\u663e\\u5f97\\u6bd4\\u8f83\\u53db\\u9006\\uff0c\\u6216\\u8005\\u4e0d\\u592a\\u613f\\u610f\\u6309\\u7167\\u522b\\u4eba\\u7684\\u671f\\u5f85\\u53bb\\u8868\\u73b0\\u81ea\\u5df1\\uff0c\\u8fd9\\u8ba9\\u4ed6\\u5728\\u4ea4\\u6d41\\u4e2d\\u6709\\u4e9b\\u6293\\u4e0d\\u7740\\u5934\\u8111\\u3002\\u5979\\u7ed9\\u4ed6\\u7684\\u611f\\u89c9\\u662f\\u6709\\u70b9\\u6297\\u62d2\\u89c4\\u8303\\u548c\\u7ea6\\u675f\\uff0c\\u50cf\\u662f\\u5728\\u6311\\u6218\\u73b0\\u6709\\u7684\\u5173\\u7cfb\\u6a21\\u5f0f\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6743\\u6756\\u4e09\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6743\\u6756\\u4e09\\u9006\\u4f4d\\u663e\\u793a\\u9ec4\\u653f\\u8c6a\\u5185\\u5fc3\\u5bf9\\u4e8e\\u559c\\u4e0d\\u559c\\u6b22\\u5f90\\u60e0\\u5b50\\u7684\\u771f\\u5b9e\\u611f\\u53d7\\u6bd4\\u8f83\\u6a21\\u7cca\\uff0c\\u751a\\u81f3\\u6709\\u70b9\\u5931\\u671b\\u6216\\u8005\\u7f3a\\u4e4f\\u4fe1\\u5fc3\\u3002\\u4ed6\\u53ef\\u80fd\\u89c9\\u5f97\\u8fd9\\u6bb5\\u5173\\u7cfb\\u7684\\u53d1\\u5c55\\u53d7\\u9650\\uff0c\\u672a\\u6765\\u6ca1\\u6709\\u592a\\u5927\\u6269\\u5c55\\u7684\\u52a8\\u529b\\u6216\\u8005\\u4ed6\\u671f\\u5f85\\u7684\\u90a3\\u79cd\\u79ef\\u6781\\u8fdb\\u5c55\\u6ca1\\u6709\\u51fa\\u73b0\\u3002\\u8fd9\\u5f20\\u724c\\u6697\\u793a\\u4ed6\\u7684\\u60c5\\u611f\\u53ef\\u80fd\\u8fd8\\u6ca1\\u5b8c\\u5168\\u5f00\\u542f\\uff0c\\u6709\\u70b9\\u505c\\u6ede\\u548c\\u72b9\\u8c6b\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u603b\\u4f53\\u6765\\u770b\\uff0c\\u5f90\\u60e0\\u5b50\\u5bf9\\u9ec4\\u653f\\u8c6a\\u76ee\\u524d\\u6765\\u8bf4\\u66f4\\u591a\\u7684\\u662f\\u4e00\\u79cd\\u8ff7\\u832b\\u548c\\u4e0d\\u786e\\u5b9a\\uff0c\\u5979\\u5185\\u5fc3\\u8fd8\\u5728\\u8bd5\\u63a2\\u548c\\u6478\\u7d22\\u5bf9\\u65b9\\uff0c\\u4e5f\\u8bb8\\u8fd8\\u6ca1\\u6709\\u5b8c\\u5168\\u655e\\u5f00\\u5fc3\\u6249\\u53bb\\u559c\\u6b22\\u4ed6\\u3002\\u9ec4\\u653f\\u8c6a\\u611f\\u53d7\\u5230\\u7684\\u662f\\u5979\\u7684\\u4e0d\\u5b89\\u548c\\u53cd\\u4f20\\u7edf\\u7684\\u6001\\u5ea6\\uff0c\\u8fd9\\u8ba9\\u4ed6\\u6709\\u4e9b\\u65e0\\u6240\\u9002\\u4ece\\uff0c\\u751a\\u81f3\\u5bf9\\u8fd9\\u6bb5\\u5173\\u7cfb\\u7684\\u53d1\\u5c55\\u611f\\u5230\\u6709\\u70b9\\u5931\\u671b\\u6216\\u8005\\u7f3a\\u4e4f\\u4fe1\\u5fc3\\u3002\\u7b54\\u6848\\u662f\\u5426\\u5b9a\\u7684\\uff0c\\u5f53\\u524d\\u5979\\u5e76\\u6ca1\\u6709\\u660e\\u786e\\u60f3\\u8868\\u8fbe\\u559c\\u6b22\\u6216\\u8005\\u6df1\\u60c5\\u7684\\u8bdd\\u8bed\\uff0c\\u800c\\u66f4\\u591a\\u662f\\u56f0\\u60d1\\u548c\\u8ddd\\u79bb\\u611f\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u5f90\\u60e0\\u5b50\\u5982\\u679c\\u60f3\\u8ba9\\u8fd9\\u6bb5\\u611f\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3112, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 1580, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 4692, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 17.714740587980486}}, \"created_at\": 1753538450}";

        //未修复 \u8fd9\u6bb5\\u", 不完整\\u系列字符串
//        text = "{\"event\": \"message\", \"task_id\": \"04fd8c0a-81f9-473e-9577-42ec098a801d\", \"id\": \"57e35860-743b-4d95-bfd5-13cdb99dd96a\", \"message_id\": \"57e35860-743b-4d95-bfd5-13cdb99dd96a\", \"conversation_id\": \"3e944801-6281-4d2c-9b7b-c9e6a71a8a25\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u5f90\\u60e0\\u5b50\\u5f53\\u4e0b\\u5bf9\\u9ec4\\u653f\\u8c6a\\u7684\\u5fc3\\u60c5\\u6709\\u4e9b\\u590d\\u6742\\uff0c\\u5185\\u5fc3\\u5145\\u6ee1\\u4e86\\u4e0d\\u786e\\u5b9a\\u548c\\u8ff7\\u832b\\u3002\\u4ed6\\u5bf9\\u8fd9\\u6bb5\\u5173\\u7cfb\\u7f3a\\u5c11\\u660e\\u786e\\u7684\\u4fe1\\u4efb\\u611f\\uff0c\\u4e5f\\u6ca1\\u6709\\u5b8c\\u5168\\u6253\\u5f00\\u5fc3\\u6249\\uff0c\\u751a\\u81f3\\u5e26\\u7740\\u4e00\\u4e9b\\u9632\\u5907\\u548c\\u56f0\\u60d1\\u5462\\u3002\\u6574\\u4f53\\u6765\\u8bf4\\uff0c\\u5979\\u73b0\\u5728\\u8fd8\\u6ca1\\u6709\\u5b8c\\u5168\\u786e\\u5b9a\\u81ea\\u5df1\\u7684\\u611f\\u60c5\\u65b9\\u5411\\uff0c\\u5bf9\\u4ed6\\u662f\\u559c\\u6b22\\u8fd8\\u53ea\\u662f\\u5b58\\u6709\\u7591\\u8651\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6708\\u4eae\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6708\\u4eae\\u6b63\\u4f4d\\u4ee3\\u8868\\u7684\\u662f\\u6726\\u80e7\\u548c\\u4e0d\\u786e\\u5b9a\\u7684\\u521d\\u5370\\u8c61\\u3002\\u5f90\\u60e0\\u5b50\\u5728\\u9ec4\\u653f\\u8c6a\\u5fc3\\u4e2d\\uff0c\\u521a\\u5f00\\u59cb\\u63a5\\u89e6\\u65f6\\u53ef\\u80fd\\u89c9\\u5f97\\u4ed6\\u795e\\u79d8\\u3001\\u6709\\u70b9\\u96be\\u4ee5\\u6349\\u6478\\uff0c\\u5979\\u770b\\u5230\\u7684\\u662f\\u4e00\\u79cd\\u590d\\u6742\\u4e14\\u6df1\\u6c89\\u7684\\u6c14\\u8d28\\uff0c\\u4f46\\u4e5f\\u4f34\\u968f\\u7740\\u5185\\u5fc3\\u7684\\u7591\\u60d1\\u548c\\u8ff7\\u832b\\u3002\\u5979\\u53ef\\u80fd\\u89c9\\u5f97\\u8fd9\\u6bb5\\u5173\\u7cfb\\u5f88\\u96be\\u4e00\\u4e0b\\u5b50\\u770b\\u6e05\\u695a\\u5168\\u8c8c\\uff0c\\u6709\\u70b9\\u50cf\\u8d70\\u5728\\u8ff7\\u96fe\\u4e2d\\uff0c\\u65e2\\u597d\\u5947\\u53c8\\u6709\\u70b9\\u4e0d\\u5b89\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6559\\u7687\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6559\\u7687\\u9006\\u4f4d\\u8bf4\\u660e\\u5728\\u65e5\\u5e38\\u76f8\\u5904\\u548c\\u804a\\u5929\\u4e2d\\uff0c\\u9ec4\\u653f\\u8c6a\\u611f\\u53d7\\u5230\\u5f90\\u60e0\\u5b50\\u6709\\u4e9b\\u53cd\\u4f20\\u7edf\\u6216\\u8005\\u4e0d\\u613f\\u610f\\u88ab\\u56fa\\u5b9a\\u5728\\u67d0\\u79cd\\u6846\\u67b6\\u91cc\\u3002\\u5979\\u53ef\\u80fd\\u663e\\u5f97\\u6bd4\\u8f83\\u53db\\u9006\\uff0c\\u6216\\u8005\\u4e0d\\u592a\\u613f\\u610f\\u6309\\u7167\\u522b\\u4eba\\u7684\\u671f\\u5f85\\u53bb\\u8868\\u73b0\\u81ea\\u5df1\\uff0c\\u8fd9\\u8ba9\\u4ed6\\u5728\\u4ea4\\u6d41\\u4e2d\\u6709\\u4e9b\\u6293\\u4e0d\\u7740\\u5934\\u8111\\u3002\\u5979\\u7ed9\\u4ed6\\u7684\\u611f\\u89c9\\u662f\\u6709\\u70b9\\u6297\\u62d2\\u89c4\\u8303\\u548c\\u7ea6\\u675f\\uff0c\\u50cf\\u662f\\u5728\\u6311\\u6218\\u73b0\\u6709\\u7684\\u5173\\u7cfb\\u6a21\\u5f0f\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6743\\u6756\\u4e09\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6743\\u6756\\u4e09\\u9006\\u4f4d\\u663e\\u793a\\u9ec4\\u653f\\u8c6a\\u5185\\u5fc3\\u5bf9\\u4e8e\\u559c\\u4e0d\\u559c\\u6b22\\u5f90\\u60e0\\u5b50\\u7684\\u771f\\u5b9e\\u611f\\u53d7\\u6bd4\\u8f83\\u6a21\\u7cca\\uff0c\\u751a\\u81f3\\u6709\\u70b9\\u5931\\u671b\\u6216\\u8005\\u7f3a\\u4e4f\\u4fe1\\u5fc3\\u3002\\u4ed6\\u53ef\\u80fd\\u89c9\\u5f97\\u8fd9\\u6bb5\\u5173\\u7cfb\\u7684\\u53d1\\u5c55\\u53d7\\u9650\\uff0c\\u672a\\u6765\\u6ca1\\u6709\\u592a\\u5927\\u6269\\u5c55\\u7684\\u52a8\\u529b\\u6216\\u8005\\u4ed6\\u671f\\u5f85\\u7684\\u90a3\\u79cd\\u79ef\\u6781\\u8fdb\\u5c55\\u6ca1\\u6709\\u51fa\\u73b0\\u3002\\u8fd9\\u5f20\\u724c\\u6697\\u793a\\u4ed6\\u7684\\u60c5\\u611f\\u53ef\\u80fd\\u8fd8\\u6ca1\\u5b8c\\u5168\\u5f00\\u542f\\uff0c\\u6709\\u70b9\\u505c\\u6ede\\u548c\\u72b9\\u8c6b\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u603b\\u4f53\\u6765\\u770b\\uff0c\\u5f90\\u60e0\\u5b50\\u5bf9\\u9ec4\\u653f\\u8c6a\\u76ee\\u524d\\u6765\\u8bf4\\u66f4\\u591a\\u7684\\u662f\\u4e00\\u79cd\\u8ff7\\u832b\\u548c\\u4e0d\\u786e\\u5b9a\\uff0c\\u5979\\u5185\\u5fc3\\u8fd8\\u5728\\u8bd5\\u63a2\\u548c\\u6478\\u7d22\\u5bf9\\u65b9\\uff0c\\u4e5f\\u8bb8\\u8fd8\\u6ca1\\u6709\\u5b8c\\u5168\\u655e\\u5f00\\u5fc3\\u6249\\u53bb\\u559c\\u6b22\\u4ed6\\u3002\\u9ec4\\u653f\\u8c6a\\u611f\\u53d7\\u5230\\u7684\\u662f\\u5979\\u7684\\u4e0d\\u5b89\\u548c\\u53cd\\u4f20\\u7edf\\u7684\\u6001\\u5ea6\\uff0c\\u8fd9\\u8ba9\\u4ed6\\u6709\\u4e9b\\u65e0\\u6240\\u9002\\u4ece\\uff0c\\u751a\\u81f3\\u5bf9\\u8fd9\\u6bb5\\u5173\\u7cfb\\u7684\\u53d1\\u5c55\\u611f\\u5230\\u6709\\u70b9\\u5931\\u671b\\u6216\\u8005\\u7f3a\\u4e4f\\u4fe1\\u5fc3\\u3002\\u7b54\\u6848\\u662f\\u5426\\u5b9a\\u7684\\uff0c\\u5f53\\u524d\\u5979\\u5e76\\u6ca1\\u6709\\u660e\\u786e\\u60f3\\u8868\\u8fbe\\u559c\\u6b22\\u6216\\u8005\\u6df1\\u60c5\\u7684\\u8bdd\\u8bed\\uff0c\\u800c\\u66f4\\u591a\\u662f\\u56f0\\u60d1\\u548c\\u8ddd\\u79bb\\u611f\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u5f90\\u60e0\\u5b50\\u5982\\u679c\\u60f3\\u8ba9\\u8fd9\\u6bb5\\u\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3112, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 1580, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 4692, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 17.714740587980486}}, \"created_at\": 1753538450}";

        //能修复 \\u 系列文本被意外换行
//        text = "{\"event\": \"message\", \"task_id\": \"f57987ba-f626-4467-961b-ad6eade0a1b7\", \"id\": \"57a96e6d-feb6-47f6-bb22-8093c073f266\", \"message_id\": \"57a96e6d-feb6-47f6-bb22-8093c073f266\", \"conversation_id\": \"f8422595-79dd-4058-b1d6-d9548f69eba7\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u4f60\\u73b0\\u5728\\u6b63\\u5904\\u4e8e\\u4e00\\u4e2a\\u5185\\u5fc3\\u6709\\u4e9b\\u8ff7\\u832b\\u548c\\u75b2\\u60eb\\u7684\\u9636\\u6bb5\\uff0c\\u6743\\u6756\\u4e00\\u9006\\u4f4d\\u663e\\u793a\\u4f60\\u53ef\\u80fd\\u611f\\u5230\\u7f3a\\u4e4f\\u52a8\\u529b\\u6216\\u65b9\\u5411\\u3002\\u9762\\u5\n" +
//                "bf9\\u8f6c\\u5b66\\u8fd9\\u4e2a\\u9009\\u62e9\\uff0c\\u724c\\u9762\\u7ed9\\u51fa\\u4e86\\u4e24\\u4e2a\\u622a\\u7136\\u4e0d\\u540c\\u7684\\u672a\\u6765\\uff1a\\u9009\\u62e9\\u7559\\u5728\\u539f\\u5b66\\u6821\\u867d\\u7136\\u4f1a\\u5e26\\u6765\\u538b\\u529b\\u548c\\u8d1f\\u62c5\\uff0c\\u4f46\\u9009\\u62e9\\u8f6c\\u5b66\\u5219\\u6697\\u793a\\u7740\\u60c5\\u611f\\u4e0a\\u7684\\u6ee1\\u8db3\\u548c\\u5185\\u5fc3\\u7684\\u5b81\\u9759\\u3002\\u603b\\u4f53\\u6765\\u8bf4\\uff0c\\u8f6c\\u5b66\\u66f4\\u6709\\u5229\\u4e8e\\u4f60\\u627e\\u5230\\u5185\\u5fc3\\u7684\\u5e73\\u8861\\u548c\\u5e78\\u798f\\u611f\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u6743\\u6756\\u4e00\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u5f20\\u724c\\u51fa\\u73b0\\u5728\\u4f60\\u73b0\\u5728\\u7684\\u72b6\\u51b5\\uff0c\\u8bf4\\u660e\\u4f60\\u76ee\\u524d\\u53ef\\u80fd\\u611f\\u5230\\u52a8\\u529b\\u4e0d\\u8db3\\uff0c\\u5185\\u5fc3\\u7684\\u70ed\\u60c5\\u88ab\\u538b\\u6291\\uff0c\\u751a\\u81f3\\u6709\\u4e9b\\u8ff7\\u832b\\u548c\\u7126\\u8651\\u3002\\u6216\\u8bb8\\u4f60\\u5bf9\\u76ee\\u524d\\u7684\\u73af\\u5883\\u4e0d\\u518d\\u6709\\u671f\\u5f85\\uff0c\\u8ba1\\u5212\\u548c\\u76ee\\u6807\\u90fd\\u53d8\\u5f97\\u6a21\\u7cca\\u3002\\u4f60\\u4f1a\\u89c9\\u5f97\\u81ea\\u5df1\\u88ab\\u675f\\u7f1a\\u4f4f\\u4e86\\uff0c\\u6e34\\u671b\\u6539\\u53d8\\u53c8\\u4e0d\\u6562\\u8f7b\\u6613\\u8fc8\\u51fa\\u6b65\\u4f10\\u3002\\u5176\\u5b9e\\uff0c\\u5185\\u5fc3\\u7684\\u8fd9\\u79cd\\u4e0d\\u5b89\\u662f\\u63d0\\u9192\\u4f60\\u9700\\u8981\\u5bfb\\u627e\\u65b0\\u7684\\u7a81\\u7834\\u53e3\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4\n" +
//                "e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u661f\\u5e01\\u4e09\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u662f\\u9009\\u62e9\\u7559\\u5728\\u539f\\u5b66\\u6821\\u7684\\u8fd1\\u672a\\u6765\\u3002\\u661f\\u5e01\\u4e09\\u9006\\u4f4d\\u6697\\u793a\\u4f60\\u7684\\u52aa\\u529b\\u53ef\\u80fd\\u5f97\\u4e0d\\u5230\\u5e94\\u6709\\u7684\\u8ba4\\u53ef\\uff0c\\u56e2\\u961f\\u534f\\u4f5c\\u548c\\u4eba\\u9645\\u5173\\u7cfb\\u4e5f\\u8bb8\\u4f1a\\u9047\\u5230\\u963b\\u788d\\uff0c\\u5bfc\\u81f4\\u4f60\\u611f\\u5230\\u5b64\\u7acb\\u548c\\u65e0\\u529b\\u3002\\u4f60\\u5728\\u8fd9\\u4e2a\\u73af\\u5883\\u4e2d\\u53ef\\u80fd\\u96be\\u4ee5\\u53d1\\u6325\\u81ea\\u5df1\\u7684\\u4f18\\u52bf\\uff0c\\u5b66\\u4e60\\u548c\\u6210\\u957f\\u7684\\u7a7a\\u95f4\\u53d7\\u9650\\uff0c\\u4f1a\\u8ba9\\u4f60\\u611f\\u5230\\u6cae\\u4e27\\u3002\\u53ef\\u4ee5\\u8bf4\\uff0c\\u7ee7\\u7eed\\u7559\\u5728\\u8fd9\\u91cc\\u77ed\\u671f\\u5185\\u4e0d\\u4f1a\\u5e26\\u6765\\u592a\\u591a\\u6b63\\u9762\\u53d8\\u5316\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u6743\\u6756\\u5341\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u662f\\u9009\\u62e9\\u7559\\u5728\\u539f\\u5b66\\u6821\\u540e\\u7684\\u7ed3\\u679c\\u3002\\u6743\\u6756\\u5341\\u4ee3\\u8868\\u91cd\\u62c5\\u548c\\u538b\\u529b\\u7684\\u7d2f\\u79ef\\uff0c\\u610f\\u5473\\u7740\\u4f60\\u4f1a\\u80cc\\u8d1f\\u8d8a\\u6765\\u8d8a\\u591a\\u7684\\u8d1f\\u62c5\\uff0c\\u611f\\u5230\\u75b2\\u60eb\\u4e0d\\u582a\\u3002\\u5b66\\u4e60\\u538b\\u529b\\u3001\\u4eba\\u9645\\u5173\\u7cfb\\u4ee5\\u53ca\\u81ea\\u8eab\\u7684\\u671f\\u671b\\u90fd\\u4f1a\\u8ba9\\u4f60\\u611f\\u5230\\u538b\\u529b\\u5c71\\u5927\\uff0c\\u751a\\u81f3\\u53ef\\u80fd\\u5f71\\u54cd\\u8eab\\u5fc3\\u5065\\u5eb7\\u3002\\u7559\\u5728\\u8fd9\\u91cc\\u867d\\u7136\\u7a33\\u5b9a\\uff0c\\u4f46\\u957f\\u671f\\u4e0b\\u6765\\u5bf9\\u4f60\\u7684\\u6210\\u957f\\u548c\\u5e78\\u798f\\u611f\\u662f\\u4e2a\\u4e0d\\u5c0f\\u7684\\u6311\\u6218\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u56db\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u5b9d\\u5251\\u516d\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u662f\\u9009\\u62e9\\u8f6c\\u5b66\\u7684\\u8fd1\\u672a\\u6765\\u3002\\u5b9d\\u5251\\u516d\\u9006\\u4f4d\\u8bf4\\u660e\\u8f6c\\u5b66\\u7684\\u521d\\u671f\\u53ef\\u80fd\\u4e0d\\u4f1a\\u90a3\\u4e48\\u987a\\u5229\\uff0c\\u53ef\\u80fd\\u4f1a\\u9047\\u5230\\u6c9f\\u901a\\u969c\\u788d\\u6216\\u8005\\u9002\\u5e94\\u56f0\\u96be\\uff0c\\u6709\\u4e9b\\u4e8b\\u60c5\\u4e0d\\u80fd\\u5982\\u4f60\\u9884\\u671f\\u822c\\u5e73\\u987a\\u5730\\u8fc7\\u6e21\\u3002\\u8fd9\\u5f20\\u724c\\u63d0\\u9192\\u4f60\\uff0c\\u8f6c\\u5b66\\u867d\\u7136\\u662f\\u5411\\u597d\\u65b9\\u5411\\u8fc8\\u8fdb\\uff0c\\u4f46\\u8fc7\\u7a0b\\u4e2d\\u7684\\u6311\\u6218\\u4e5f\\u9700\\u8981\\u4f60\\u53bb\\u9762\\u5bf9\\u548c\\u514b\\u670d\\u5462\\u3002\\u4e0d\\u8981\\u6015\\u4e00\\u6b65\\u4e00\\u6b65\\u6765\\uff0c\\u8c03\\u6574\\u5fc3\\u6001\\u6700\\u91cd\\u8981\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e94\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u5723\\u676f\\u4e5d\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u662f\\u9009\\u62e9\\u8f6c\\u5b66\\u540e\\u7684\\u7ed3\\u679c\\u3002\\u5723\\u676f\\u4e5d\\u662f\\u975e\\u5e38\\u7f8e\\u597d\\u7684\\u724c\\uff0c\\u4ee3\\u8868\\u6ee1\\u8db3\\u611f\\u548c\\u5e78\\u798f\\u611f\\uff0c\\u4f60\\u5728\\u65b0\\u7684\\u73af\\u5883\\u91cc\\u66f4\\u5bb9\\u6613\\u627e\\u5230\\u5fc3\\u7075\\u7684\\u6ee1\\u8db3\\u548c\\u5feb\\u4e50\\u3002\\u4f60\\u4f1a\\u611f\\u53d7\\u5230\\u81ea\\u5df1\\u7684\\u52aa\\u529b\\u88ab\\u8ba4\\u53ef\\uff0c\\u4e5f\\u6709\\u66f4\\u591a\\u673a\\u4f1a\\u7ed3\\u8bc6\\u652f\\u6301\\u4f60\\u7684\\u4eba\\u3002\\u6574\\u4f53\\u6765\\u770b\\uff0c\\u8f6c\\u5b66\\u6709\\u671b\\u5e26\\u7ed9\\u4f60\\u66f4\\u597d\\u7684\\u6210\\u957f\\u4f53\\u9a8c\\u548c\\u5185\\u5fc3\\u7684\\u5e73\\u548c\\uff0c\\u8fd9\\u4efd\\u6ee1\\u8db3\\u5c06\\u662f\\u4f60\\u575a\\u6301\\u4e0b\\u53bb\\u7684\\u52a8\\u529b\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u7ed3\\u5408\\u724c\\u9762\\u6765\\u770b\\uff0c\\u7559\\u5728\\u539f\\u5b66\\u6821\\u4f60\\u4f1a\\u9762\\u4e34\\u8d8a\\u6765\\u8d8a\\u591a\\u7684\\u538b\\u529b\\u548c\\u75b2\\u60eb\\uff0c\\u73af\\u5883\\u4e0d\\u5229\\u4e8e\\u4f60\\u7684\\u53d1\\u6325\\u548c\\u6210\\u957f\\uff1b\\u800c\\u9009\\u62e9\\u8f6c\\u5b66\\uff0c\\u867d\\u7136\\u4f1a\\u9047\\u5230\\u9002\\u5e94\\u4e0a\\u7684\\u6311\\u6218\\uff0c\\u4f46\\u6700\\u7ec8\\u4f1a\\u5e26\\u6765\\u5185\\u5fc3\\u7684\\u6ee1\\u8db3\\u548c\\u5e78\\u798f\\u3002\\u4f60\\u7684\\u5185\\u5fc3\\u5176\\u5b9e\\u5df2\\u7ecf\\u5728\\u6e34\\u671b\\u4e00\\u79cd\\u65b0\\u7684\\u5f00\\u59cb\\uff0c\\u8f6c\\u5b66\\u662f\\u901a\\u5f80\\u66f4\\u597d\\u672a\\u6765\\u7684\\u95e8\\u69db\\uff0c\\u53ea\\u662f\\u9700\\u8981\\u4f60\\u6709\\u52c7\\u6c14\\u8de8\\u8fc7\\u53bb\\u54e6\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u6211\\u5efa\\u8bae\\u4f60\\u52c7\\u6562\\u9009\\u62e9\\u8f6c\\u5b66\\uff0c\\u5c3d\\u7ba1\\u8fc7\\u7a0b\\u53ef\\u80fd\\u5e76\\u4e0d\\u8f7b\\u677e\\uff0c\\u4f46\\u957f\\u8fdc\\u6765\\u770b\\u8fd9\\u5bf9\\u4f60\\u66f4\\u6709\\u5229\\u3002\\u4f60\\u53ef\\u4ee5\\u63d0\\u524d\\u505a\\u597d\\u5fc3\\u7406\\u51c6\\u5907\\uff0c\\u79ef\\u6781\\u8c03\\u6574\\u5fc3\\u6001\\uff0c\\u6bd4\\u5982\\u591a\\u548c\\u65b0\\u73af\\u5883\\u7684\\u4eba\\u6c9f\\u901a\\uff0c\\u4e3b\\u52a8\\u9002\\u5e94\\u65b0\\u7684\\u5b66\\u4e60\\u65b9\\u5f0f\\u548c\\u751f\\u6d3b\\u8282\\u594f\\u3002\\u4e5f\\u53ef\\u4ee5\\u8bbe\\u5b9a\\u5c0f\\u76ee\\u6807\\uff0c\\u8ba9\\u81ea\\u5df1\\u4e00\\u70b9\\u70b9\\u627e\\u5230\\u5f52\\u5c5e\\u611f\\u548c\\u6210\\u5c31\\u611f\\u3002\\u8bb0\\u5f97\\u591a\\u7ed9\\u81ea\\u5df1\\u9f13\\u52b1\\u548c\\u8010\\u5fc3\\uff0c\\u4f60\\u503c\\u5f97\\u62e5\\u6709\\u66f4\\u597d\\u7684\\u6210\\u957f\\u73af\\u5883\\u5440\\uff01\\\"\\n  }\\n]\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3740, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 2505, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 6245, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 22.216494099004194}}, \"created_at\": 1753649417}";

        //能修复 \"card_name\": \\\"\u6743\u6756\u5341\u9006\u4f4d\\\"
//        text = "{\"event\": \"message\", \"task_id\": \"f9785b05-7776-4542-870f-5965e5e98e58\", \"id\": \"6dccc9ad-f69d-4f99-9831-886e656f5c1e\", \"message_id\": \"6dccc9ad-f69d-4f99-9831-886e656f5c1e\", \"conversation_id\": \"9291b30f-2d5f-4fac-bb7f-3f2ea4e4ed5a\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u7ec4\\u724c\\u900f\\u9732\\u51fa\\u4f60\\u4eec\\u4e4b\\u95f4\\u73b0\\u5728\\u5173\\u7cfb\\u6709\\u4e9b\\u6c89\\u91cd\\u548c\\u590d\\u6742\\uff0c\\u544a\\u767d\\u7684\\u8def\\u4e0a\\u53ef\\u80fd\\u5e76\\u4e0d\\u8f7b\\u677e\\u3002\\u4f46\\u724c\\u9762\\u4e5f\\u63d0\\u9192\\u4f60\\uff0c\\u91cd\\u8981\\u7684\\u662f\\u8c03\\u6574\\u597d\\u81ea\\u5df1\\u7684\\u72b6\\u6001\\uff0c\\u7167\\u987e\\u597d\\u81ea\\u5df1\\uff0c\\u627e\\u5230\\u9002\\u5408\\u7684\\u65b9\\u5f0f\\u53bb\\u8868\\u8fbe\\u5fc3\\u610f\\uff0c\\u624d\\u6709\\u53ef\\u80fd\\u8d70\\u5230\\u4e00\\u8d77\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u6743\\u6756\\u5341\\u9006\\u4f4d\\\\\\\",\\n    \\\"description\\\": \\\"\\u4f60\\u5728\\u5bf9\\u65b9\\u5fc3\\u4e2d\\u7684\\u5370\\u8c61\\u662f\\u6709\\u4e9b\\u8d1f\\u62c5\\u548c\\u538b\\u529b\\uff0c\\u6743\\u6756\\u5341\\u9006\\u4f4d\\u663e\\u793a\\u4f60\\u53ef\\u80fd\\u7ed9\\u5bf9\\u65b9\\u5e26\\u6765\\u4e86\\u7d2f\\u8d58\\u6216\\u8005\\u8fc7\\u591a\\u7684\\u8d23\\u4efb\\u611f\\uff0c\\u5bf9\\u65b9\\u611f\\u89c9\\u8fd9\\u6bb5\\u5173\\u7cfb\\u8ba9\\u4ed6\\u6709\\u70b9\\u5403\\u4e0d\\u6d88\\u5462\\u3002\\u4e5f\\u8bb8\\u4f60\\u6700\\u8fd1\\u8868\\u73b0\\u5f97\\u5f88\\u62fc\\u547d\\uff0c\\u60f3\\u9760\\u52aa\\u529b\\u8ba9\\u5bf9\\u65b9\\u6ce8\\u610f\\uff0c\\u4f46\\u53cd\\u800c\\u8ba9\\u4ed6\\u89c9\\u5f97\\u538b\\u529b\\u5c71\\u5927\\u3002\\u522b\\u7070\\u5fc3\\uff0c\\u8fd9\\u8bf4\\u660e\\u4f60\\u5f88\\u91cd\\u89c6\\uff0c\\u53ea\\u662f\\u65b9\\u5f0f\\u9700\\u8981\\u8c03\\u6574\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u7687\\u540e\\u6b63\\u4f4d\\\\\\\",\\n    \\\"description\\\": \\\"\\u544a\\u767d\\u8fc7\\u7a0b\\u4e2d\\u53ef\\u80fd\\u9047\\u5230\\u7684\\u963b\\u788d\\u662f\\u5bf9\\u65b9\\u7684\\u5173\\u6000\\u548c\\u654f\\u611f\\u3002\\u7687\\u540e\\u6b63\\u4f4d\\u63d0\\u9192\\uff0c\\u53ef\\u80fd\\u5bf9\\u65b9\\u73b0\\u5728\\u6b63\\u5904\\u4e8e\\u7167\\u987e\\u522b\\u4eba\\u6216\\u8005\\u88ab\\u522b\\u4eba\\u7167\\u987e\\u7684\\u72b6\\u6001\\uff0c\\u4ed6\\u7684\\u60c5\\u7eea\\u548c\\u5fc3\\u601d\\u90fd\\u6bd4\\u8f83\\u7ec6\\u817b\\uff0c\\u751a\\u81f3\\u6709\\u70b9\\u88ab\\u4fdd\\u62a4\\u6b32\\u5305\\u88f9\\u7740\\uff0c\\u6240\\u4ee5\\u4e0d\\u5bb9\\u6613\\u63a5\\u53d7\\u65b0\\u7684\\u611f\\u60c5\\u3002\\u4f60\\u53ef\\u80fd\\u9700\\u8981\\u66f4\\u591a\\u8010\\u5fc3\\u53bb\\u7406\\u89e3\\u4ed6\\uff0c\\u800c\\u4e0d\\u662f\\u6025\\u7740\\u63a8\\u8fdb\\u611f\\u60c5\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u661f\\u5e01\\u7687\\u540e\\u9006\\u4f4d\\\\\\\",\\n    \\\"description\\\": \\\"\\u544a\\u767d\\u7684\\u6700\\u7ec8\\u7ed3\\u679c\\u663e\\u793a\\u73b0\\u5b9e\\u65b9\\u9762\\u7684\\u969c\\u788d\\u5f88\\u5927\\uff0c\\u661f\\u5e01\\u7687\\u540e\\u9006\\u4f4d\\u4ee3\\u8868\\u7ecf\\u6d4e\\u3001\\u7a33\\u5b9a\\u6027\\u6216\\u7269\\u8d28\\u5c42\\u9762\\u7684\\u95ee\\u9898\\uff0c\\u6697\\u793a\\u4f60\\u4eec\\u4e4b\\u95f4\\u53ef\\u80fd\\u56e0\\u4e3a\\u751f\\u6d3b\\u8282\\u594f\\u6216\\u7ecf\\u6d4e\\u72b6\\u51b5\\u4e0d\\u5339\\u914d\\uff0c\\u5bfc\\u81f4\\u611f\\u60c5\\u96be\\u4ee5\\u7a33\\u5b9a\\u53d1\\u5c55\\u3002\\u5bf9\\u65b9\\u53ef\\u80fd\\u89c9\\u5f97\\u76ee\\u524d\\u65f6\\u673a\\u4e0d\\u6210\\u719f\\uff0c\\u6216\\u8005\\u89c9\\u5f97\\u4f60\\u4eec\\u5728\\u751f\\u6d3b\\u76ee\\u6807\\u4e0a\\u6709\\u8f83\\u5927\\u5206\\u6b67\\uff0c\\u9700\\u8981\\u4e09\\u601d\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u56db\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u6218\\u8f66\\u9006\\u4f4d\\\\\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u5f20\\u6307\\u5f15\\u724c\\u544a\\u8bc9\\u4f60\\u73b0\\u5728\\u7684\\u4f60\\u53ef\\u80fd\\u7f3a\\u4e4f\\u5185\\u5fc3\\u7684\\u65b9\\u5411\\u611f\\uff0c\\u60c5\\u7eea\\u6216\\u884c\\u52a8\\u4e0a\\u6709\\u4e9b\\u5931\\u63a7\\uff0c\\u544a\\u767d\\u524d\\u8981\\u5148\\u6574\\u7406\\u597d\\u81ea\\u5df1\\u7684\\u8282\\u594f\\u548c\\u5fc3\\u6001\\u3002\\u76f2\\u76ee\\u51b2\\u52a8\\u6216\\u5f3a\\u884c\\u63a8\\u8fdb\\u611f\\u60c5\\u53ea\\u4f1a\\u8ba9\\u60c5\\u51b5\\u66f4\\u7cdf\\u3002\\u5efa\\u8bae\\u4f60\\u5148\\u6682\\u505c\\uff0c\\u7ed9\\u81ea\\u5df1\\u4e00\\u70b9\\u65f6\\u95f4\\uff0c\\u8c03\\u6574\\u597d\\u72b6\\u6001\\uff0c\\u518d\\u51b3\\u5b9a\\u662f\\u5426\\u544a\\u767d\\u4f1a\\u66f4\\u5408\\u9002\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u7efc\\u5408\\u6765\\u770b\\uff0c\\u4f60\\u4eec\\u76ee\\u524d\\u8ddd\\u79bb\\u8d70\\u5230\\u4e00\\u8d77\\u8fd8\\u6709\\u4e9b\\u969c\\u788d\\u3002\\u4f60\\u5728\\u5bf9\\u65b9\\u5fc3\\u91cc\\u504f\\u91cd\\u8d1f\\u62c5\\u611f\\uff0c\\u5bf9\\u65b9\\u60c5\\u7eea\\u654f\\u611f\\u4e14\\u73b0\\u5b9e\\u5c42\\u9762\\u6709\\u987e\\u8651\\uff0c\\u544a\\u767d\\u6210\\u529f\\u7684\\u53ef\\u80fd\\u6027\\u6bd4\\u8f83\\u4f4e\\u3002\\u73b0\\u5728\\u4e0d\\u662f\\u5f3a\\u884c\\u8868\\u767d\\u7684\\u597d\\u65f6\\u673a\\uff0c\\u66f4\\u591a\\u662f\\u9700\\u8981\\u4f60\\u5148\\u8c03\\u6574\\u597d\\u81ea\\u5df1\\uff0c\\u7ed9\\u5f7c\\u6b64\\u65f6\\u95f4\\u548c\\u7a7a\\u95f4\\u3002\\u611f\\u60c5\\u662f\\u4e24\\u4e2a\\u4eba\\u7684\\u4e8b\\u60c5\\uff0c\\u65f6\\u673a\\u548c\\u73af\\u5883\\u90fd\\u5f88\\u91cd\\u8981\\uff0c\\u4e0d\\u6025\\u4e8e\\u4e00\\u65f6\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u5148\\u4e0d\\u8981\\u6025\\u7740\\u544a\\u767d\\u54e6\\uff0c\\u91cd\\u70b9\\u5148\\u653e\\u5728\\u63d0\\u5347\\u81ea\\u5df1\\u7684\\u751f\\u6d3b\\u8d28\\u91cf\\u4e0a\\uff0c\\u6bd4\\u5982\\u7a33\\u5b9a\\u7ecf\\u6d4e\\u72b6\\u51b5\\u3001\\u7406\\u987a\\u5de5\\u4f5c\\u6216\\u5b66\\u4e1a\\u538b\\u529b\\uff0c\\u8fd9\\u6837\\u4f60\\u4f1a\\u66f4\\u81ea\\u4fe1\\uff0c\\u4e5f\\u66f4\\u6709\\u5438\\u5f15\\u529b\\u3002\\u518d\\u8005\\uff0c\\u8bd5\\u7740\\u7528\\u6e29\\u548c\\u7684\\u65b9\\u5f0f\\u589e\\u8fdb\\u4e86\\u89e3\\uff0c\\u4e0d\\u8981\\u8ba9\\u5bf9\\u65b9\\u611f\\u5230\\u8d1f\\u62c5\\u3002\\u53ef\\u4ee5\\u5173\\u6ce8\\u81ea\\u5df1\\u7684\\u60c5\\u7eea\\u7ba1\\u7406\\u548c\\u884c\\u52a8\\u8282\\u594f\\uff0c\\u627e\\u5230\\u9002\\u5408\\u81ea\\u5df1\\u7684\\u8282\\u594f\\uff0c\\u6162\\u6162\\u8ba9\\u5f7c\\u6b64\\u7684\\u5173\\u7cfb\\u7a33\\u56fa\\u8d77\\u6765\\u3002\\u611f\\u60c5\\u7684\\u4e8b\\uff0c\\u7a33\\u624e\\u7a33\\u6253\\u624d\\u662f\\u957f\\u4e45\\u4e4b\\u9053\\u5462\\u3002\\\"\\n  }\\n]\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3165, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 2023, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 5188, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 13.158524725935422}}, \"created_at\": 1753641685}";

        //未修复 json业务数据错误
//        text = "{\"event\": \"message\", \"task_id\": \"d77cfc2e-21dd-45a7-86e3-44d529244b6e\", \"id\": \"f7d4a757-1907-4a4b-a73d-bd3eb68d57fa\",\n" +
//                " \"message_id\": \"f7d4a757-1907-4a4b-a73d-bd3eb68d57fa\", \"conversation_id\": \"33af4715-264d-42d7-81dd-e47638b51de7\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u4f60\\u5bf9\\u\n" +
//                "660e\\u5e74\\u62e5\\u6709\\u5b9d\\u5b9d\\u5145\\u6ee1\\u671f\\u5f85\\uff0c\\u4e5f\\u5728\\u79ef\\u6781\\u505a\\u8ba1\\u5212\\u548c\\u51c6\\u5907\\uff0c\\u724c\\u9762\\u6574\\u4f53\\u7ed9\\u51fa\\u7684\\u4fe1\\u606f\\u662f\\uff0c\\u867d\\u7136\\u6709\\u5f88\\u5f3a\\u7684\\u63a8\\u5\n" +
//                "2a8\\u529b\\u548c\\u7a33\\u5b9a\\u7684\\u57fa\\u7840\\uff0c\\u4f46\\u76ee\\u524d\\u4ecd\\u5b58\\u5728\\u4e00\\u4e9b\\u963b\\u788d\\uff0c\\u9700\\u8981\\u8010\\u5fc3\\u548c\\u5177\\u4f53\\u7684\\u884c\\u52a8\\u6765\\u7a81\\u7834\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u\n" +
//                "7b2c\\u4e00\\u5f20\\u724c\\\",\\n\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3066, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 227, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"co\n" +
//                "mpletion_price\": \"0E-7\", \"total_tokens\": 3293, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 21.444452291936614}}, \"created_at\": 1753639735}";

        //能修复 \\u 系列文本 \u5bf9\u4ed6\u5440\u3002\"]" 缺失结束 } 符号
//        text = "{\"event\": \"message\", \"task_id\": \"1002524c-3e8f-4fc4-8dba-7c1078dd9d14\", \"id\": \"bed3d310-fc37-4aea-8041-b38832c76010\",\n" +
//                " \"message_id\": \"bed3d310-fc37-4aea-8041-b38832c76010\", \"conversation_id\": \"f47ae700-1b94-4ac1-9c8c-21fc55be1585\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u4f60\\u73b0\\u\n" +
//                "5728\\u5f88\\u5173\\u5fc3\\u4ed6\\u6709\\u6ca1\\u6709\\u5728\\u60f3\\u7740\\u4f60\\uff0c\\u60f3\\u77e5\\u9053\\u4ed6\\u5fc3\\u91cc\\u7684\\u771f\\u5b9e\\u72b6\\u51b5\\u3002\\u7687\\u5e1d\\u9006\\u4f4d\\u8bf4\\u660e\\uff0c\\u4ed6\\u53ef\\u80fd\\u5e76\\u6ca1\\u6709\\u50cf\\u4f60\\u6\n" +
//                "0f3\\u8c61\\u7684\\u90a3\\u6837\\u7a33\\u91cd\\u6216\\u8005\\u8d1f\\u8d23\\u4efb\\u5730\\u770b\\u5f85\\u4f60\\u4eec\\u7684\\u5173\\u7cfb\\uff0c\\u6216\\u8005\\u4ed6\\u8fd9\\u6bb5\\u65f6\\u95f4\\u5fc3\\u60c5\\u4e0a\\u6709\\u4e9b\\u6df7\\u4e71\\uff0c\\u4e0d\\u592a\\u80fd\\u7406\\u6e\n" +
//                "05\\u695a\\u81ea\\u5df1\\u7684\\u60c5\\u7eea\\u548c\\u60f3\\u6cd5\\u3002\\u4f60\\u60f3\\u8981\\u7684\\u662f\\u4e00\\u4e2a\\u660e\\u786e\\u7684\\u56de\\u5e94\\uff0c\\u4f46\\u4ed6\\u73b0\\u5728\\u53ef\\u80fd\\u8fd8\\u6ca1\\u51c6\\u5907\\u597d\\u7ed9\\u51fa\\u7b54\\u6848\\uff0c\\u8fd\n" +
//                "9\\u79cd\\u72b6\\u6001\\u4f1a\\u8ba9\\u4f60\\u6709\\u70b9\\u8ff7\\u832b\\u548c\\u5931\\u843d\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u672c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u7687\\u5e1d\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u7687\\u5e1d\\u9006\\u4f\n" +
//                "4d\\u8868\\u793a\\u4ed6\\u5728\\u60f3\\u4f60\\u7684\\u65f6\\u5019\\uff0c\\u53ef\\u80fd\\u66f4\\u591a\\u662f\\u611f\\u5230\\u538b\\u529b\\u6216\\u8005\\u4e0d\\u5b89\\uff0c\\u800c\\u4e0d\\u662f\\u6e29\\u6696\\u548c\\u5b89\\u5fc3\\u3002\\u4ed6\\u53ef\\u80fd\\u5728\\u751f\\u6d3b\\u621\n" +
//                "6\\u5de5\\u4f5c\\u4e2d\\u9047\\u5230\\u4e86\\u6311\\u6218\\uff0c\\u5bfc\\u81f4\\u4ed6\\u53d8\\u5f97\\u4e0d\\u591f\\u679c\\u65ad\\uff0c\\u751a\\u81f3\\u6709\\u4e9b\\u9003\\u907f\\u8d23\\u4efb\\u3002\\u53ef\\u80fd\\u4ed6\\u4e0d\\u613f\\u610f\\u6b63\\u89c6\\u4f60\\u4eec\\u4e4b\\u95f4\n" +
//                "\\u7684\\u95ee\\u9898\\uff0c\\u6216\\u8005\\u5bb3\\u6015\\u627f\\u62c5\\u611f\\u60c5\\u4e0a\\u7684\\u627f\\u8bfa\\u3002\\u8fd9\\u5f20\\u724c\\u900f\\u9732\\u51fa\\u4ed6\\u73b0\\u5728\\u7684\\u5fc3\\u6001\\u6bd4\\u8f83\\u6df7\\u4e71\\uff0c\\u751a\\u81f3\\u6709\\u4e9b\\u63a7\\u5236\\\n" +
//                "u6b32\\u5931\\u8861\\uff0c\\u4ed6\\u53ef\\u80fd\\u5728\\u6323\\u624e\\u4e2d\\u8ff7\\u5931\\u65b9\\u5411\\uff0c\\u6240\\u4ee5\\u4f60\\u611f\\u89c9\\u4e0d\\u5230\\u4ed6\\u7684\\u5173\\u5fc3\\u6216\\u601d\\u5ff5\\u7684\\u70ed\\u5ea6\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\n" +
//                "\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u9762\\u5bf9\\u8fd9\\u6837\\u7684\\u60c5\\u51b5\\uff0c\\u4f60\\u9700\\u8981\\u5148\\u628a\\u5173\\u6ce8\\u70b9\\u653e\\u56de\\u81ea\\u5df1\\u8eab\\u4e0a\\uff0c\\u800c\\u4e0d\\u662f\\u4e00\\u76f4\\u671f\\u5f85\\u4ed6\\u7ed9\\u51fa\\u660e\\u7\n" +
//                "86e\\u7684\\u56de\\u5e94\\u3002\\u7687\\u5e1d\\u9006\\u4f4d\\u8bf4\\u660e\\u4ed6\\u73b0\\u5728\\u4e0d\\u591f\\u6210\\u719f\\u6216\\u8005\\u6ca1\\u6709\\u51c6\\u5907\\u597d\\u627f\\u62c5\\u8d23\\u4efb\\uff0c\\u4f60\\u53ef\\u4ee5\\u8003\\u8651\\u5148\\u7ed9\\u81ea\\u5df1\\u8bbe\\u7a\n" +
//                "cb\\u4e00\\u4e9b\\u751f\\u6d3b\\u548c\\u60c5\\u611f\\u4e0a\\u7684\\u5b89\\u5168\\u611f\\u3002\\u591a\\u82b1\\u65f6\\u95f4\\u63d0\\u5347\\u81ea\\u5df1\\u7684\\u5de5\\u4f5c\\u80fd\\u529b\\u6216\\u8005\\u5174\\u8da3\\u7231\\u597d\\uff0c\\u5efa\\u7acb\\u81ea\\u5df1\\u7684\\u7a33\\u5b9\n" +
//                "a\\u611f\\uff0c\\u8fd9\\u6837\\u65e0\\u8bba\\u4ed6\\u60f3\\u4e0d\\u60f3\\u4f60\\uff0c\\u4f60\\u90fd\\u6709\\u81ea\\u5df1\\u7684\\u575a\\u5b9e\\u57fa\\u7840\\u3002\\u73b0\\u5b9e\\u4e00\\u70b9\\uff0c\\u53bb\\u5173\\u6ce8\\u63d0\\u5347\\u81ea\\u5df1\\u8d22\\u52a1\\u548c\\u804c\\u4e1a\n" +
//                "\\u7684\\u7a33\\u5b9a\\uff0c\\u4fdd\\u6301\\u5185\\u5fc3\\u7684\\u5f3a\\u5927\\uff0c\\u8fd9\\u6837\\u624d\\u4f1a\\u771f\\u6b63\\u5438\\u5f15\\u5bf9\\u7684\\u4eba\\uff0c\\u5438\\u5f15\\u4ed6\\u771f\\u6b63\\u8d70\\u5411\\u4f60\\u3002\\u7b49\\u4ed6\\u7406\\u6e05\\u695a\\u5fc3\\u601d\\\n" +
//                "uff0c\\u4f60\\u4e5f\\u66f4\\u6709\\u5e95\\u6c14\\u53bb\\u9762\\u5bf9\\u4ed6\\u5440\\u3002\\\"\\n]\", \"metadata\": {\"usage\": {\"prompt_tokens\": 2024, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 1177, \"comple\n" +
//                "tion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 3201, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 4.717466961010359}}, \"created_at\": 1753639521}";

        //能修复 \\u 系列文本 \"card_name\": \\\"\u661f\u5e01\u4e5d\u9006\u4f4d\\\" 缺失结束 } 符号
        text = "{\"event\": \"message\", \"task_id\": \"ab6ee118-2e51-4a53-9582-ec2882971514\", \"id\": \"6ec8dc0f-8aca-4784-8cf1-c813036c93f3\",\n" +
                " \"message_id\": \"6ec8dc0f-8aca-4784-8cf1-c813036c93f3\", \"conversation_id\": \"7c460516-c17b-4f1d-80d4-9b74d1d388ec\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u4f60\\u73b0\\u\n" +
                "5728\\u6b63\\u5904\\u4e8e\\u4e00\\u4e2a\\u9700\\u8981\\u505a\\u51fa\\u91cd\\u5927\\u804c\\u4e1a\\u9009\\u62e9\\u7684\\u5173\\u53e3\\uff0c\\u5185\\u5fc3\\u53ef\\u80fd\\u6709\\u4e9b\\u72b9\\u8c6b\\u548c\\u4e0d\\u5b89\\u3002\\u724c\\u9762\\u544a\\u8bc9\\u6211\\u4eec\\uff0c\\u6362\\u5\n" +
                "c97\\uff08\\u9009\\u62e9A\\uff09\\u66f4\\u6709\\u6d3b\\u529b\\u548c\\u52a8\\u529b\\uff0c\\u4e5f\\u66f4\\u80fd\\u8ba9\\u4f60\\u638c\\u63a7\\u81ea\\u5df1\\u7684\\u65b9\\u5411\\uff0c\\u800c\\u6362\\u5355\\u4f4d\\uff08\\u9009\\u62e9B\\uff09\\u867d\\u7136\\u770b\\u8d77\\u6765\\u662f\\u\n" +
                "7a33\\u59a5\\u7684\\u9009\\u62e9\\uff0c\\u4f46\\u672a\\u6765\\u53ef\\u80fd\\u9762\\u4e34\\u66f4\\u591a\\u7684\\u6311\\u6218\\u548c\\u4e0d\\u987a\\u3002\\u8fd9\\u4e2a\\u65f6\\u523b\\uff0c\\u52c7\\u6562\\u8fc8\\u51fa\\u81ea\\u5df1\\u771f\\u6b63\\u60f3\\u8d70\\u7684\\u8def\\uff0c\\u6\n" +
                "24d\\u662f\\u6700\\u91cd\\u8981\\u7684\\u5462\\uff01\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u661f\\u5e01\\u4e5d\\u9006\\u4f4d\\\\\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u5f20\\u724c\\u4ee3\\u8868\\u4f60\\u76ee\\u524d\\u76\n" +
                "84\\u72b6\\u6001\\uff0c\\u661f\\u5e01\\u4e5d\\u9006\\u4f4d\\u8868\\u660e\\u4f60\\u5bf9\\u73b0\\u72b6\\u611f\\u5230\\u4e0d\\u6ee1\\u8db3\\uff0c\\u53ef\\u80fd\\u662f\\u5de5\\u4f5c\\u4e0a\\u6ca1\\u6709\\u83b7\\u5f97\\u5e94\\u6709\\u7684\\u56de\\u62a5\\u6216\\u8005\\u6210\\u5c31\\u611\n" +
                "f\\uff0c\\u4e5f\\u8bb8\\u4f60\\u89c9\\u5f97\\u81ea\\u5df1\\u52aa\\u529b\\u5f97\\u4e0d\\u5230\\u8ba4\\u53ef\\uff0c\\u6216\\u8005\\u6536\\u5165\\u548c\\u53d1\\u5c55\\u7a7a\\u95f4\\u90fd\\u53d7\\u9650\\u3002\\u4f60\\u53ef\\u80fd\\u6b63\\u7ecf\\u5386\\u4e00\\u79cd\\u5185\\u5fc3\\u7684\n" +
                "\\u56f0\\u987f\\u548c\\u75b2\\u60eb\\uff0c\\u611f\\u89c9\\u81ea\\u5df1\\u88ab\\u56f0\\u5728\\u4e00\\u4e2a\\u6ca1\\u6709\\u6210\\u957f\\u7684\\u74f6\\u9888\\u91cc\\u3002\\u522b\\u62c5\\u5fc3\\uff0c\\u8fd9\\u5176\\u5b9e\\u662f\\u63d0\\u9192\\u4f60\\u8be5\\u505a\\u51fa\\u6539\\u53d8\\\n" +
                "u7684\\u65f6\\u5019\\u4e86\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u611a\\u8005\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u9009\\u62e9\\u6362\\u5c97\\u7684\\u8bdd\\uff0c\\u611a\\u8005\\u6b63\\u4f4d\\u4ee3\n" +
                "\\u8868\\u4e00\\u79cd\\u5168\\u65b0\\u7684\\u5f00\\u59cb\\u548c\\u5192\\u9669\\u7cbe\\u795e\\u3002\\u4f60\\u4f1a\\u6709\\u673a\\u4f1a\\u8df3\\u51fa\\u65e7\\u6709\\u7684\\u6846\\u67b6\\uff0c\\u53bb\\u5c1d\\u8bd5\\u65b0\\u7684\\u804c\\u8d23\\u548c\\u6311\\u6218\\u3002\\u8fd9\\u5f20\\\n" +
                "u724c\\u5145\\u6ee1\\u81ea\\u7531\\u548c\\u5e0c\\u671b\\uff0c\\u6697\\u793a\\u4f60\\u80fd\\u5728\\u65b0\\u7684\\u5c97\\u4f4d\\u4e0a\\u91cd\\u65b0\\u627e\\u5230\\u70ed\\u60c5\\u548c\\u6d3b\\u529b\\u3002\\u867d\\u7136\\u672a\\u77e5\\u5e26\\u6765\\u4e0d\\u786e\\u5b9a\\uff0c\\u4f46\\u\n" +
                "5b83\\u4e5f\\u610f\\u5473\\u7740\\u65e0\\u9650\\u53ef\\u80fd\\uff0c\\u8fd9\\u662f\\u8ba9\\u4f60\\u91cd\\u65b0\\u71c3\\u8d77\\u52a8\\u529b\\u7684\\u673a\\u4f1a\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6218\\u8f6\n" +
                "6\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6362\\u5c97\\u7684\\u7ed3\\u679c\\u662f\\u6218\\u8f66\\u6b63\\u4f4d\\uff0c\\u8bf4\\u660e\\u4f60\\u80fd\\u591f\\u51ed\\u501f\\u81ea\\u5df1\\u7684\\u610f\\u5fd7\\u529b\\u548c\\u51b3\\u5fc3\\u53d6\\u5f97\\u80dc\\u5229\\u3002\\u4f60\\u\n" +
                "4f1a\\u638c\\u63a7\\u5c40\\u9762\\uff0c\\u514b\\u670d\\u5de5\\u4f5c\\u4e2d\\u7684\\u5404\\u79cd\\u56f0\\u96be\\uff0c\\u53d6\\u5f97\\u660e\\u663e\\u7684\\u8fdb\\u6b65\\u548c\\u6210\\u5c31\\u3002\\u6218\\u8f66\\u4e5f\\u4ee3\\u8868\\u7740\\u81ea\\u4fe1\\u548c\\u52a8\\u529b\\uff0c\\u9\n" +
                "884\\u793a\\u4f60\\u5728\\u65b0\\u5c97\\u4f4d\\u4e0a\\u4f1a\\u6709\\u6240\\u7a81\\u7834\\uff0c\\u751a\\u81f3\\u53ef\\u80fd\\u83b7\\u5f97\\u9886\\u5bfc\\u529b\\u6216\\u66f4\\u9ad8\\u7684\\u8ba4\\u53ef\\u3002\\u6362\\u5c97\\u8ba9\\u4f60\\u8fce\\u6765\\u4e86\\u4e00\\u4e2a\\u4e3b\\u52\n" +
                "a8\\u51fa\\u51fb\\u3001\\u5f00\\u521b\\u672a\\u6765\\u7684\\u673a\\u4f1a\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u56db\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6743\\u6756\\u4e5d\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u9009\\u62e9\\u6362\\u5355\\u4\n" +
                "f4d\\u7684\\u8bdd\\uff0c\\u6743\\u6756\\u4e5d\\u6b63\\u4f4d\\u4ee3\\u8868\\u4f60\\u4f1a\\u8fdb\\u5165\\u4e00\\u4e2a\\u76f8\\u5bf9\\u9632\\u5b88\\u7684\\u72b6\\u6001\\uff0c\\u9700\\u8981\\u4e0d\\u65ad\\u5e94\\u5bf9\\u6311\\u6218\\u548c\\u538b\\u529b\\u3002\\u4f60\\u53ef\\u80fd\\u4f\n" +
                "1a\\u611f\\u89c9\\u5230\\u5468\\u56f4\\u73af\\u5883\\u5145\\u6ee1\\u7ade\\u4e89\\u548c\\u8003\\u9a8c\\uff0c\\u9700\\u8981\\u4f60\\u4e0d\\u65ad\\u575a\\u5b88\\u548c\\u52aa\\u529b\\uff0c\\u4fdd\\u62a4\\u81ea\\u5df1\\u4e0d\\u88ab\\u51fb\\u5012\\u3002\\u867d\\u7136\\u6709\\u575a\\u630\n" +
                "1\\u7684\\u4ef7\\u503c\\uff0c\\u4f46\\u8fd9\\u6761\\u8def\\u5bf9\\u4f60\\u7684\\u7cbe\\u529b\\u548c\\u5fc3\\u7406\\u8003\\u9a8c\\u8f83\\u5927\\uff0c\\u4f1a\\u611f\\u5230\\u75b2\\u60eb\\u548c\\u7126\\u8651\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e94\\u5f20\\u72\n" +
                "4c\\\",\\n    \\\"card_name\\\": \\\"\\u6743\\u6756\\u516d\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6362\\u5355\\u4f4d\\u7684\\u6700\\u7ec8\\u7ed3\\u679c\\u51fa\\u73b0\\u4e86\\u6743\\u6756\\u516d\\u9006\\u4f4d\\uff0c\\u8fd9\\u6697\\u793a\\u7740\\u4f60\\u5728\\u65b0\\u7684\\u535\n" +
                "5\\u4f4d\\u53ef\\u80fd\\u96be\\u4ee5\\u83b7\\u5f97\\u9884\\u671f\\u7684\\u6210\\u529f\\u548c\\u8ba4\\u53ef\\u3002\\u4f60\\u53ef\\u80fd\\u4f1a\\u611f\\u5230\\u88ab\\u5ffd\\u89c6\\u3001\\u7f3a\\u5c11\\u652f\\u6301\\uff0c\\u6216\\u8005\\u906d\\u9047\\u632b\\u8d25\\u611f\\u3002\\u56e2\n" +
                "\\u961f\\u91cc\\u53ef\\u80fd\\u7f3a\\u4e4f\\u5408\\u4f5c\\u6c1b\\u56f4\\uff0c\\u6216\\u8005\\u4f60\\u4e2a\\u4eba\\u7684\\u52aa\\u529b\\u96be\\u4ee5\\u5e26\\u6765\\u5e94\\u6709\\u7684\\u6210\\u6548\\u3002\\u6574\\u4f53\\u6765\\u770b\\uff0c\\u8fd9\\u6761\\u8def\\u5bb9\\u6613\\u8ba9\\\n" +
                "u4f60\\u611f\\u5230\\u5931\\u843d\\u548c\\u6cae\\u4e27\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u7efc\\u5408\\u6765\\u770b\\uff0c\\u4f60\\u76ee\\u524d\\u5bf9\\u73b0\\u72b6\\u611f\\u5230\\u4e0d\\u6ee1\\uff0c\\u6362\\u5c97\\u\n" +
                "ff08\\u9009\\u62e9A\\uff09\\u7ed9\\u4f60\\u5e26\\u6765\\u7684\\u662f\\u65b0\\u9c9c\\u611f\\u548c\\u6210\\u957f\\u7684\\u673a\\u4f1a\\uff0c\\u672a\\u6765\\u80fd\\u8ba9\\u4f60\\u51ed\\u501f\\u81ea\\u5df1\\u7684\\u52aa\\u529b\\u83b7\\u5f97\\u6210\\u529f\\u548c\\u8ba4\\u53ef\\u3002\\u\n" +
                "800c\\u6362\\u5355\\u4f4d\\uff08\\u9009\\u62e9B\\uff09\\u867d\\u7136\\u770b\\u4f3c\\u662f\\u5927\\u6b65\\u8c03\\u6574\\uff0c\\u4f46\\u53ef\\u80fd\\u4f1a\\u9762\\u5bf9\\u66f4\\u591a\\u6311\\u6218\\u548c\\u632b\\u6298\\uff0c\\u6210\\u5c31\\u611f\\u548c\\u652f\\u6301\\u90fd\\u6709\\u\n" +
                "9650\\u3002\\u5f88\\u660e\\u663e\\uff0c\\u5854\\u7f57\\u724c\\u5728\\u544a\\u8bc9\\u4f60\\uff0c\\u6362\\u5c97\\u624d\\u662f\\u5bf9\\u4f60\\u66f4\\u6709\\u76ca\\u7684\\u9009\\u62e9\\uff0c\\u80fd\\u591f\\u771f\\u6b63\\u6539\\u53d8\\u4f60\\u76ee\\u524d\\u7684\\u56f0\\u5883\\u3002\\\"\\\n" +
                "n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u65e2\\u7136\\u724c\\u9762\\u5982\\u6b64\\u660e\\u786e\\uff0c\\u6211\\u5efa\\u8bae\\u4f60\\u679c\\u65ad\\u9009\\u62e9\\u6362\\u5c97\\uff01\\u5728\\u9009\\u62e9\\u4e4b\\u524d\\uff0c\\u4f60\\u53ef\\u4e\n" +
                "e5\\u597d\\u597d\\u68b3\\u7406\\u81ea\\u5df1\\u60f3\\u8981\\u53d1\\u5c55\\u7684\\u65b9\\u5411\\u548c\\u5174\\u8da3\\uff0c\\u9009\\u62e9\\u90a3\\u4e2a\\u6700\\u80fd\\u6fc0\\u53d1\\u4f60\\u70ed\\u60c5\\u548c\\u6f5c\\u529b\\u7684\\u5c97\\u4f4d\\u3002\\u540c\\u65f6\\uff0c\\u4fdd\\u630\n" +
                "1\\u5f00\\u653e\\u7684\\u5fc3\\u6001\\uff0c\\u52c7\\u6562\\u63a5\\u53d7\\u65b0\\u6311\\u6218\\uff0c\\u4e0d\\u6015\\u72af\\u9519\\uff0c\\u6218\\u8f66\\u544a\\u8bc9\\u4f60\\u53ea\\u8981\\u575a\\u6301\\uff0c\\u80dc\\u5229\\u7ec8\\u5c06\\u5c5e\\u4e8e\\u4f60\\u3002\\u6362\\u5c97\\u540e\n" +
                "\\u8981\\u6ce8\\u610f\\u8c03\\u6574\\u5fc3\\u6001\\uff0c\\u7ba1\\u7406\\u597d\\u538b\\u529b\\u548c\\u671f\\u671b\\uff0c\\u79ef\\u6781\\u5b66\\u4e60\\u548c\\u63d0\\u5347\\uff0c\\u624d\\u80fd\\u8ba9\\u8fd9\\u6b21\\u8f6c\\u53d8\\u5e26\\u6765\\u771f\\u6b63\\u7684\\u6210\\u957f\\u5440\\\n" +
                "uff01\\u800c\\u6362\\u5355\\u4f4d\\u867d\\u7136\\u8bf1\\u4eba\\uff0c\\u4f46\\u76ee\\u524d\\u98ce\\u9669\\u548c\\u538b\\u529b\\u8f83\\u5927\\uff0c\\u5efa\\u8bae\\u4f60\\u6682\\u65f6\\u628a\\u91cd\\u5fc3\\u653e\\u5728\\u73b0\\u6709\\u5355\\u4f4d\\u7684\\u5c97\\u4f4d\\u53d8\\u52a8\\u\n" +
                "4e0a\\uff0c\\u7b49\\u673a\\u4f1a\\u66f4\\u6210\\u719f\\u65f6\\u518d\\u8003\\u8651\\u5927\\u6b65\\u8de8\\u8d8a\\u5462\\u3002\\\"\\n  }\\n]\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3742, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \n" +
                "\"completion_tokens\": 2754, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 6496, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 6.486357362009585}}, \"created_at\": 1753635933}";

//        text = "{\"event\": \"message\", \"task_id\": \"5c19d892-c5da-4f2c-8d28-4dfae5c43730\", \"id\": \"ba313998-4b49-40b6-a483-8fcd9800afb6\",\n" +
//                " \"message_id\": \"ba313998-4b49-40b6-a483-8fcd9800afb6\", \"conversation_id\": \"e6b62354-6cf7-4c5d-ac27-0033ae56de9b\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u4ece\\u8fd9\\u\n" +
//                "7ec4\\u724c\\u6765\\u770b\\uff0cTa\\u5bf9\\u4f60\\u7684\\u5fc3\\u52a8\\u6307\\u6570\\u5e76\\u4e0d\\u9ad8\\uff0c\\u751a\\u81f3\\u5e26\\u6709\\u4e00\\u5b9a\\u7684\\u77db\\u76fe\\u548c\\u4fdd\\u7559\\u3002Ta\\u5bf9\\u4f60\\u7684\\u7b2c\\u4e00\\u5370\\u8c61\\u548c\\u5185\\u5fc3\\u771\n" +
//                "f\\u5b9e\\u611f\\u53d7\\u90fd\\u663e\\u793a\\u51fa\\u4e0d\\u591f\\u7a33\\u5b9a\\u548c\\u4e0d\\u786e\\u5b9a\\uff0c\\u8bf4\\u660eTa\\u76ee\\u524d\\u5728\\u611f\\u60c5\\u4e0a\\u53ef\\u80fd\\u8fd8\\u6ca1\\u6709\\u5b8c\\u5168\\u6253\\u5f00\\uff0c\\u5fc3\\u91cc\\u8fd8\\u5e26\\u7740\\u98\n" +
//                "7e\\u8651\\u5462\\u3002\\u4f60\\u503c\\u5f97\\u88ab\\u5168\\u5fc3\\u5168\\u610f\\u559c\\u6b22\\uff0c\\u6240\\u4ee5\\u4e5f\\u8bf7\\u7ed9\\u81ea\\u5df1\\u4e00\\u70b9\\u65f6\\u95f4\\u548c\\u7a7a\\u95f4\\uff0c\\u611f\\u60c5\\u7684\\u706b\\u82b1\\u662f\\u53ef\\u4ee5\\u6162\\u6162\\u88a\n" +
//                "b\\u70b9\\u71c3\\u7684\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\\\\\"\\u8282\\u5236\\u9006\\u4f4d\\\\\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u5f20\\u724c\\u4ee3\\u8868Ta\\u5bf9\\u4f60\\u7684\\u7b2c\\u4e00\\u5370\\u8c\n" +
//                "61\\u5e76\\u4e0d\\u5e73\\u8861\\uff0c\\u6216\\u8bb8\\u89c9\\u5f97\\u4f60\\u4eec\\u4e4b\\u95f4\\u7f3a\\u4e4f\\u67d0\\u79cd\\u534f\\u8c03\\u548c\\u9ed8\\u5951\\u3002Ta\\u53ef\\u80fd\\u89c9\\u5f97\\u4f60\\u4eec\\u7684\\u8282\\u594f\\u4e0d\\u592a\\u5408\\u62cd\\uff0c\\u751a\\u81f3\\u8\n" +
//                "9c9\\u5f97\\u4f60\\u6709\\u4e9b\\u96be\\u4ee5\\u6349\\u6478\\uff0c\\u6216\\u8005\\u4f60\\u4eec\\u7684\\u80fd\\u91cf\\u4e0d\\u5728\\u4e00\\u4e2a\\u9891\\u7387\\u4e0a\\u3002Ta\\u5728\\u6700\\u521d\\u89c1\\u4f60\\u7684\\u65f6\\u5019\\uff0c\\u5185\\u5fc3\\u662f\\u6709\\u4e9b\\u4e0d\\u\n" +
//                "5b89\\u548c\\u8ff7\\u832b\\u7684\\u5462\\uff0c\\u4e0d\\u8fc7\\u8fd9\\u4e5f\\u5e76\\u4e0d\\u4ee3\\u8868Ta\\u5bf9\\u4f60\\u5b8c\\u5168\\u6ca1\\u5174\\u8da3\\uff0c\\u53ea\\u662f\\u7b2c\\u4e00\\u5370\\u8c61\\u4e0d\\u662f\\u7279\\u522b\\u8212\\u670d\\u3002\\u8bb0\\u5f97\\uff0c\\u7b2c\\\n" +
//                "u4e00\\u5370\\u8c61\\u53ef\\u4ee5\\u88ab\\u540e\\u6765\\u76f8\\u5904\\u4fee\\u6b63\\u7684\\u5440\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u661f\\u5e01\\u4e5d\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u8fd9\\u5f20\n" +
//                "\\u724c\\u663e\\u793a\\u51fa\\u5728\\u65e5\\u5e38\\u76f8\\u5904\\u548c\\u804a\\u5929\\u4e2d\\uff0cTa\\u89c9\\u5f97\\u4f60\\u662f\\u4e00\\u4e2a\\u72ec\\u7acb\\u4e14\\u751f\\u6d3b\\u7a33\\u5b9a\\u7684\\u4eba\\uff0cTa\\u53ef\\u80fd\\u5f88\\u6b23\\u8d4f\\u4f60\\u81ea\\u7ed9\\u81ea\\u8\n" +
//                "db3\\u3001\\u6709\\u4e3b\\u89c1\\u7684\\u6837\\u5b50\\u3002Ta\\u5bf9\\u4f60\\u7684\\u611f\\u53d7\\u662f\\u6bd4\\u8f83\\u6b63\\u9762\\u7684\\uff0c\\u89c9\\u5f97\\u4f60\\u751f\\u6d3b\\u6709\\u54c1\\u5473\\uff0c\\u6709\\u81ea\\u5df1\\u7684\\u4ef7\\u503c\\u3002Ta\\u5728\\u4f60\\u8eab\n" +
//                "\\u8fb9\\u611f\\u53d7\\u5230\\u4e00\\u79cd\\u5b89\\u5168\\u611f\\u548c\\u73b0\\u5b9e\\u7684\\u5438\\u5f15\\u529b\\uff0c\\u8fd9\\u7b97\\u662f\\u4e0d\\u9519\\u7684\\u4fe1\\u53f7\\u5462\\u3002\\u4e0d\\u8fc7\\u8fd9\\u66f4\\u591a\\u662f\\u7406\\u6027\\u5c42\\u9762\\u7684\\u8ba4\\u53ef\\\n" +
//                "uff0c\\u800c\\u4e0d\\u4e00\\u5b9a\\u662f\\u60c5\\u611f\\u4e0a\\u7684\\u6df1\\u523b\\u5fc3\\u52a8\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6743\\u6756\\u9a91\\u58eb\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u8fd9\n" +
//                "\\u5f20\\u724c\\u8bf4\\u660eTa\\u5bf9\\u4f60\\u7684\\u771f\\u5b9e\\u611f\\u53d7\\u662f\\u6709\\u4e9b\\u72b9\\u8c6b\\u548c\\u4e0d\\u786e\\u5b9a\\u7684\\uff0cTa\\u5185\\u5fc3\\u53ef\\u80fd\\u6709\\u51b2\\u52a8\\u6216\\u8005\\u60f3\\u8981\\u9760\\u8fd1\\u4f60\\u7684\\u51b2\\u52a8\\uf\n" +
//                "f0c\\u4f46\\u53c8\\u56e0\\u4e3a\\u67d0\\u4e9b\\u5185\\u5728\\u6216\\u5916\\u5728\\u7684\\u539f\\u56e0\\u800c\\u9000\\u7f29\\u4e86\\u3002Ta\\u53ef\\u80fd\\u5728\\u884c\\u52a8\\u4e0a\\u8868\\u73b0\\u51fa\\u8fdf\\u7591\\uff0c\\u751a\\u81f3\\u4f1a\\u6709\\u4e9b\\u7f3a\\u4e4f\\u52a8\\u\n" +
//                "529b\\u6216\\u8005\\u4fe1\\u5fc3\\u53bb\\u8ffd\\u6c42\\u4f60\\u3002\\u8fd9\\u8bf4\\u660e\\uff0cTa\\u73b0\\u5728\\u5e76\\u6ca1\\u6709\\u771f\\u6b63\\u88ab\\u4f60\\u6df1\\u6df1\\u5438\\u5f15\\uff0c\\u6216\\u8005\\u8bf4\\u8fd9\\u4efd\\u5fc3\\u52a8\\u8fd8\\u6ca1\\u5230\\u8ba9Ta\\u656\n" +
//                "2\\u4e8e\\u8fc8\\u51fa\\u91cd\\u8981\\u4e00\\u6b65\\u7684\\u7a0b\\u5ea6\\u5462\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u6574\\u4f53\\u6765\\u770b\\uff0cTa\\u5bf9\\u4f60\\u7684\\u5fc3\\u52a8\\u4e0d\\u7b97\\u5f3a\\u70c8\\uff0c\\u75\n" +
//                "1a\\u81f3\\u6709\\u4e9b\\u6447\\u6446\\u4e0d\\u5b9a\\u3002Ta\\u5bf9\\u4f60\\u6709\\u7406\\u6027\\u7684\\u8ba4\\u53ef\\u548c\\u6b23\\u8d4f\\uff0c\\u4f46\\u60c5\\u611f\\u5c42\\u9762\\u8fd8\\u6ca1\\u5b8c\\u5168\\u6253\\u5f00\\uff0c\\u53ef\\u80fd\\u662f\\u56e0\\u4e3a\\u5185\\u5fc3\\u4\n" +
//                "e0d\\u591f\\u786e\\u5b9a\\u6216\\u8005\\u5bf9\\u63a5\\u4e0b\\u6765\\u5173\\u7cfb\\u7684\\u65b9\\u5411\\u611f\\u5230\\u8ff7\\u832b\\u3002Ta\\u5bf9\\u4f60\\u7684\\u7b2c\\u4e00\\u5370\\u8c61\\u4e5f\\u5e26\\u6709\\u4e9b\\u987e\\u8651\\uff0c\\u867d\\u7136\\u65e5\\u5e38\\u76f8\\u5904\\u\n" +
//                "4e2d\\u6709\\u79ef\\u6781\\u611f\\u53d7\\uff0c\\u4f46\\u5185\\u5fc3\\u6df1\\u5904\\u7684\\u60c5\\u611f\\u63a8\\u52a8\\u529b\\u4e0d\\u8db3\\u3002\\u7b80\\u800c\\u8a00\\u4e4b\\uff0cTa\\u6682\\u65f6\\u8fd8\\u6ca1\\u6709\\u771f\\u6b63\\u5fc3\\u52a8\\u5462\\u3002\\\"\\n  },\\n  {\\n    \n" +
//                "\\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u5efa\\u8bae\\u4f60\\u5148\\u4e0d\\u8981\\u6025\\u7740\\u60f3\\u8ba9Ta\\u5fc3\\u52a8\\uff0c\\u7ed9Ta\\u4e00\\u70b9\\u65f6\\u95f4\\u548c\\u7a7a\\u95f4\\u53bb\\u771f\\u6b63\\u4e86\\u89e3\\u4f60\\u3002\\u4f60\\u53ef\\u4e\n" +
//                "e5\\u5728\\u4ea4\\u5f80\\u4e2d\\u591a\\u5c55\\u73b0\\u4f60\\u771f\\u5b9e\\u4e14\\u6709\\u6e29\\u5ea6\\u7684\\u4e00\\u9762\\uff0c\\u9002\\u5f53\\u5206\\u4eab\\u4f60\\u7684\\u751f\\u6d3b\\u548c\\u5185\\u5fc3\\u611f\\u53d7\\uff0c\\u589e\\u52a0\\u4eb2\\u5bc6\\u611f\\u548c\\u4fe1\\u4ef\n" +
//                "b\\u3002\\u4e5f\\u53ef\\u4ee5\\u901a\\u8fc7\\u4e00\\u4e9b\\u8f7b\\u677e\\u6109\\u5feb\\u7684\\u4e92\\u52a8\\uff0c\\u51cf\\u5c11Ta\\u7684\\u987e\\u8651\\uff0c\\u8ba9Ta\\u611f\\u89c9\\u548c\\u4f60\\u5728\\u4e00\\u8d77\\u662f\\u8f7b\\u677e\\u81ea\\u5728\\u7684\\u3002\\u6700\\u91cd\\u\n" +
//                "8981\\u7684\\u662f\\u4fdd\\u6301\\u81ea\\u6211\\u4ef7\\u503c\\uff0c\\u522b\\u56e0\\u4e3a\\u60f3\\u8981\\u88ab\\u559c\\u6b22\\u800c\\u8fc7\\u5ea6\\u8fc1\\u5c31\\uff0c\\u771f\\u6b63\\u7684\\u559c\\u6b22\\u662f\\u76f8\\u4e92\\u5438\\u5f15\\u7684\\u5440\\u3002\\u6162\\u6162\\u6765\\uf\n" +
//                "f0c\\u771f\\u5fc3\\u7684\\u4eba\\u4f1a\\u8d70\\u8fd1\\u4f60\\u7684\\u3002\\\"\\n  }\\n]\", \"metadata\": {\"usage\": {\"prompt_tokens\": 3100, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 2027, \"completion_unit\n" +
//                "_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 5127, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 10.159474069951102}}, \"created_at\": 1753686712}";

        text = "{\"event\": \"message\", \"task_id\": \"33df6256-bf7e-4cd0-a454-46737319d5fd\", \"id\": \"354188d9-85c0-4164-8e5e-e0550b8a1f68\", \"message_id\": \"354188d9-85c0-4164-8e5e-e0550b8a1f68\", \"conversation_id\": \"7c15b62e-29f8-40f7-8b4c-3dd451bb4d1d\", \"mode\": \"advanced-chat\", \"answer\": \"[\\n  {\\n    \\\"label\\\": \\\"\\u6574\\u4f53\\u611f\\u53d7\\\",\\n    \\\"description\\\": \\\"\\u4ece\\u724c\\u9762\\u6765\\u770b\\uff0c\\u8fd9\\u6bb5\\u53cb\\u8c0a\\u672a\\u6765\\u6709\\u4e00\\u5b9a\\u7684\\u6311\\u6218\\uff0c\\u5173\\u7cfb\\u4e0d\\u4f1a\\u81ea\\u7136\\u800c\\u7136\\u53d8\\u5f97\\u66f4\\u7d27\\u5bc6\\uff0c\\u53cd\\u800c\\u6709\\u758f\\u8fdc\\u7684\\u53ef\\u80fd\\u6027\\u5462\\u3002\\u4e0d\\u8fc7\\uff0c\\u6311\\u6218\\u5e76\\u4e0d\\u4ee3\\u8868\\u7ec8\\u7ed3\\uff0c\\u53ea\\u662f\\u63d0\\u9192\\u4f60\\u4eec\\u9700\\u8981\\u66f4\\u591a\\u7684\\u52aa\\u529b\\u548c\\u6c9f\\u901a\\u624d\\u80fd\\u7ef4\\u7cfb\\u597d\\u8fd9\\u6bb5\\u5173\\u7cfb\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e00\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u6743\\u6756\\u4e03\\u6b63\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u6743\\u6756\\u4e03\\u6b63\\u4f4d\\u8868\\u793a\\u76ee\\u524d\\u4f60\\u548c\\u670b\\u53cb\\u4e4b\\u95f4\\u5b58\\u5728\\u4e00\\u4e9b\\u7ade\\u4e89\\u6216\\u9632\\u5b88\\u7684\\u6001\\u5ea6\\uff0c\\u53ef\\u80fd\\u4f60\\u4eec\\u90fd\\u5728\\u4e3a\\u81ea\\u5df1\\u7684\\u7acb\\u573a\\u575a\\u6301\\uff0c\\u6216\\u662f\\u6709\\u4eba\\u611f\\u53d7\\u5230\\u9700\\u8981\\u4fdd\\u62a4\\u81ea\\u5df1\\u4e0d\\u88ab\\u5ffd\\u89c6\\u6216\\u8005\\u4f24\\u5bb3\\u3002\\u4f60\\u4eec\\u4e4b\\u95f4\\u7684\\u4e92\\u52a8\\u5e26\\u7740\\u4e00\\u70b9\\u7d27\\u5f20\\u611f\\uff0c\\u53cc\\u65b9\\u90fd\\u6ca1\\u6709\\u8f7b\\u6613\\u653e\\u4e0b\\u6212\\u5907\\u5462\\u3002\\u5c3d\\u7ba1\\u8fd9\\u6837\\uff0c\\u6743\\u6756\\u4e03\\u4e5f\\u4ee3\\u8868\\u4e00\\u79cd\\u52c7\\u6c14\\u548c\\u575a\\u6301\\uff0c\\u8fd9\\u8bf4\\u660e\\u4f60\\u4eec\\u90fd\\u5728\\u610f\\u8fd9\\u6bb5\\u53cb\\u8c0a\\uff0c\\u4e0d\\u613f\\u8f7b\\u6613\\u653e\\u5f03\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e8c\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u5723\\u676f\\u7687\\u540e\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u5723\\u676f\\u7687\\u540e\\u9006\\u4f4d\\u6697\\u793a\\u60c5\\u611f\\u65b9\\u9762\\u7684\\u5931\\u8861\\uff0c\\u53ef\\u80fd\\u4f60\\u6216\\u5bf9\\u65b9\\u5728\\u8fd9\\u6bb5\\u53cb\\u8c0a\\u4e2d\\u611f\\u53d7\\u5230\\u60c5\\u7eea\\u88ab\\u5ffd\\u89c6\\u6216\\u4e0d\\u88ab\\u7406\\u89e3\\uff0c\\u751a\\u81f3\\u6709\\u4e9b\\u60c5\\u7eea\\u9632\\u5fa1\\uff0c\\u96be\\u4ee5\\u771f\\u8bda\\u8868\\u8fbe\\u5185\\u5fc3\\u3002\\u5979\\u9006\\u4f4d\\u901a\\u5e38\\u53cd\\u6620\\u51fa\\u60c5\\u7eea\\u7684\\u4e0d\\u7a33\\u5b9a\\u6216\\u8fc7\\u4e8e\\u654f\\u611f\\uff0c\\u5bfc\\u81f4\\u8bef\\u4f1a\\u548c\\u9694\\u9602\\u6ecb\\u751f\\u3002\\u4e5f\\u8bb8\\u6709\\u4eba\\u5e26\\u7740\\u4f24\\u75db\\u6216\\u8005\\u671f\\u5f85\\u843d\\u7a7a\\uff0c\\u53cb\\u8c0a\\u91cc\\u9700\\u8981\\u7684\\u662f\\u66f4\\u591a\\u7684\\u7406\\u89e3\\u548c\\u8010\\u5fc3\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u7b2c\\u4e09\\u5f20\\u724c\\\",\\n    \\\"card_name\\\": \\\"\\u7687\\u540e\\u9006\\u4f4d\\\",\\n    \\\"description\\\": \\\"\\u7687\\u540e\\u9006\\u4f4d\\u8fdb\\u4e00\\u6b65\\u5f3a\\u8c03\\u4e86\\u60c5\\u611f\\u7684\\u56f0\\u5883\\u548c\\u5173\\u7cfb\\u4e2d\\u7684\\u4e0d\\u5e73\\u8861\\u3002\\u8fd9\\u5f20\\u724c\\u663e\\u793a\\u6709\\u4eba\\u53ef\\u80fd\\u5728\\u8fd9\\u6bb5\\u5173\\u7cfb\\u4e2d\\u8868\\u73b0\\u51fa\\u60c5\\u7eea\\u4e0a\\u7684\\u81ea\\u6211\\u4e2d\\u5fc3\\uff0c\\u6216\\u662f\\u7f3a\\u4e4f\\u5173\\u6000\\u548c\\u652f\\u6301\\uff0c\\u5bfc\\u81f4\\u53cc\\u65b9\\u7684\\u4eb2\\u5bc6\\u5ea6\\u53d7\\u5230\\u5f71\\u54cd\\u3002\\u4f60\\u4eec\\u53ef\\u80fd\\u9700\\u8981\\u8b66\\u60d5\\u60c5\\u7eea\\u4e0a\\u7684\\u758f\\u8fdc\\u548c\\u51b7\\u6f20\\uff0c\\u907f\\u514d\\u8ba9\\u8d1f\\u9762\\u60c5\\u7eea\\u79ef\\u7d2f\\uff0c\\u7834\\u574f\\u4e86\\u670b\\u53cb\\u95f4\\u7684\\u4fe1\\u4efb\\u548c\\u6e29\\u6696\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u603b\\u7ed3\\\",\\n    \\\"description\\\": \\\"\\u6574\\u4f53\\u6765\\u770b\\uff0c\\u8fd9\\u6bb5\\u53cb\\u8c0a\\u672a\\u6765\\u503e\\u5411\\u4e8e\\u758f\\u8fdc\\u800c\\u975e\\u66f4\\u7d27\\u5bc6\\u3002\\u6743\\u6756\\u4e03\\u663e\\u793a\\u53cc\\u65b9\\u90fd\\u8f83\\u4e3a\\u9632\\u5907\\uff0c\\u5723\\u676f\\u7687\\u540e\\u9006\\u4f4d\\u548c\\u7687\\u540e\\u9006\\u4f4d\\u5219\\u63ed\\u793a\\u60c5\\u611f\\u4e0a\\u7684\\u4e0d\\u5e73\\u8861\\u548c\\u6c9f\\u901a\\u969c\\u788d\\u3002\\u8981\\u60f3\\u8d70\\u5f97\\u66f4\\u8fd1\\uff0c\\u9700\\u8981\\u53cc\\u65b9\\u6253\\u5f00\\u5fc3\\u6249\\uff0c\\u771f\\u8bda\\u9762\\u5bf9\\u5f7c\\u6b64\\u7684\\u60c5\\u7eea\\u548c\\u9700\\u6c42\\uff0c\\u5426\\u5219\\u5f88\\u5bb9\\u6613\\u9677\\u5165\\u8bef\\u89e3\\u548c\\u51b7\\u6f20\\u3002\\\"\\n  },\\n  {\\n    \\\"label\\\": \\\"\\u5efa\\u8bae\\\",\\n    \\\"description\\\": \\\"\\u5efa\\u8bae\\u4f60\\u9996\\u5148\\u8c03\\u6574\\u5fc3\\u6001\\uff0c\\u653e\\u4e0b\\u9632\\u5907\\uff0c\\u4e3b\\u52a8\\u8868\\u8fbe\\u81ea\\u5df1\\u7684\\u771f\\u5b9e\\u611f\\u53d7\\u548c\\u671f\\u5f85\\uff0c\\u7ed9\\u5bf9\\u65b9\\u4e00\\u4e2a\\u673a\\u4f1a\\u53bb\\u7406\\u89e3\\u4f60\\u3002\\u540c\\u65f6\\uff0c\\u4e5f\\u8981\\u7559\\u610f\\u5bf9\\u65b9\\u7684\\u60c5\\u7eea\\u53d8\\u5316\\uff0c\\u4e0d\\u8981\\u4e00\\u5473\\u6307\\u8d23\\uff0c\\u8bd5\\u7740\\u7528\\u6e29\\u67d4\\u548c\\u8010\\u5fc3\\u6765\\u5316\\u89e3\\u9694\\u9602\\u3002\\u53ef\\u4ee5\\u627e\\u4e00\\u4e2a\\u9002\\u5408\\u7684\\u65f6\\u673a\\uff0c\\u5766\\u8bda\\u5730\\u804a\\u804a\\u4f60\\u4eec\\u7684\\u5173\\u7cfb\\u73b0\\u72b6\\u548c\\u672a\\u6765\\u613f\\u666f\\uff0c\\u907f\\u514d\\u60c5\\u7eea\\u7d2f\\u79ef\\u5bfc\\u81f4\\u66f4\\u5927\\u7684\\u88c2\\u75d5\\u3002\\u52a1\\u5b9e\\u5730\\u7ef4\\u62a4\\u8fd9\\u6bb5\\u53cb\\u8c0a\\uff0c\\u624d\\u6709\\u53ef\\u80fd\\u8ba9\\u5b83\\u4e0d\\u518d\\u758f\\u8fdc\\u3002\\u8bb0\\u5f97\\uff0c\\u771f\\u8bda\\u548c\\u6c9f\\u901a\\u662f\\u7ef4\\u7cfb\\u53cb\\u8c0a\\u6700\\u91cd\\u8981\\u7684\\u6865\\u6881\\u5440\\u3002\\\"\\n  }\\n]\", \"metadata\": {\"usage\": {\"prompt_tokens\": 2818, \"prompt_unit_price\": \"0\", \"prompt_price_unit\": \"0\", \"prompt_price\": \"0E-7\", \"completion_tokens\": 1814, \"completion_unit_price\": \"0\", \"completion_price_unit\": \"0\", \"completion_price\": \"0E-7\", \"total_tokens\": 4632, \"total_price\": \"0E-7\", \"currency\": \"USD\", \"latency\": 7.543726550997235}}, \"created_at\": 1753755069}";


        DEBUG = true;
        JSONObject object = repairAnswer(text);
        if(DEBUG) log.info("response = {}", object);
    }

    public static JSONObject repairAnswer(String text) {
        if(DEBUG) log.info("text 0: {}", text);

        text = repairResponse(text);
        if(DEBUG) log.info("text 1: {}", text);
        if (text == null || text.isEmpty()) {
            throw new RuntimeException("塔罗解读结果为空");
        }

        JSONObject respJson = JSONUtil.parseObj(text);

        if (!respJson.containsKey("answer")) {
            return respJson;
        }

        text = respJson.getStr("answer");
        if(DEBUG) log.info("text 2: {}", text);
        if (text == null || text.isEmpty()) {
            throw new RuntimeException("塔罗解读结果Json数据内容为空");
        }
        if(text.startsWith("[") || text.startsWith("{")){
            text = repairJsonText(text);
            if(DEBUG) log.info("text 3: {}", text);

            String jsonStr = jsonParse(text);
            if(DEBUG) log.info("repair: {}", jsonStr);

            if (jsonStr != null && jsonStr.startsWith("[")) {
                JSONArray jsonArray = JSONUtil.parseArray(jsonStr);
                if (ObjectUtil.isNotEmpty(jsonArray)) {
                    JSONObject endJson = jsonArray.getJSONObject(jsonArray.size() - 1);
                    if (!endJson.containsKey("label")
                            || !"建议".equals(endJson.get("label"))
                            || !endJson.containsKey("description")
                    ) {
                        if (DEBUG) log.error("塔罗解读结果Json业务数据错误: {}", jsonStr);
                        throw new RuntimeException("塔罗解读结果是无效Json数据格式");
                    }
                }
            }else{
                log.error("塔罗解读结果Json数据: {}", text);
                throw new RuntimeException("塔罗解读结果Json数据格式错误");
            }
            if(DEBUG) log.info("success = {}", StrUtil.isNotBlank(jsonStr));
            text = jsonStr;
        }else{
            text = text.replaceAll(regex, "");
        }

        respJson.set("answer", text);
        if(DEBUG) log.info("result: {}", respJson);
        log.info("塔罗解读结果Json数据: {}", respJson);

        return respJson;
    }

    private static boolean isJson(String text){
        return JSONUtil.isTypeJSON(text);
    }

    private static String jsonParse(String text) {
        if(StrUtil.isBlank(text)){
            return null;
        }
        String jsonStr;

        //尝试使用 jackson 转换
        jsonStr = jacksonString(text);
        if (jsonStr != null) {
            if(DEBUG) log.info("jacksonParse: {}", jsonStr);
            return jsonStr;
        }

        //尝试使用 gson 转换
        jsonStr = gsonString(text);
        if (jsonStr != null) {
            if(DEBUG) log.info("gsonParse: {}", jsonStr);
            return jsonStr;
        }

        return null;
    }

    private static String repairJsonText(String text){
        if(StrUtil.isBlank(text)){
            return null;
        }
        String jsonStr = jsonParse(text);
        if(StrUtil.isNotBlank(jsonStr)){
            return jsonStr;
        }

        // 原文替换 \\" 转为 \"
        text = text.replaceAll("\\\\\"", "\"");

        // 修复未加引号的 key
        text = text.replaceAll("(\\w+)(?=:)", "\"$1\"");

        if(text.startsWith("[")){
            if(!text.endsWith("]")){
                text += "]";
            }

            int lastIdx = text.lastIndexOf("{");
            String lastJson = text.substring(lastIdx);
            if(lastJson.startsWith("{") & lastJson.indexOf("}")< 1 ){
                lastJson = lastJson.replace("]", "}]");
            }
            text = text.substring(0, lastIdx) + lastJson;
        }

        if(text.startsWith("{") & !text.endsWith("}")){
            text += "}";
        }
        if(DEBUG) log.info("repairJsonText 修复: {}", text);

        jsonStr = jsonParse(text);
        if(StrUtil.isNotBlank(jsonStr)){
            return jsonStr;
        }

        text = unescape(text);

        jsonStr = jsonParse(text);
        if(StrUtil.isNotBlank(jsonStr)){
            return jsonStr;
        }

        text = jsonRepair(text);

        return text;
    }

    private static String jsonRepair(String text){
        try {
            text = JSON_REPAIR.handle(text);
            if(DEBUG) log.info("jsonRepair 修复: {}", text);
            return text;
        } catch (Exception e) {
            throw new RuntimeException("json-repair 修复异常: "+e.getMessage());
        }
    }

    private static String unescape(String text){
        if(text.contains("\\u")){
            try {
                text = EscapeUtil.unescape(text);
                if(DEBUG) log.info("unescape 转换: {}", text);
                return text;
            } catch (Exception e) {
                throw new RuntimeException("unescape 转换异常: "+e.getMessage());
            }
        }
        return text;
    }

    private static String repairResponse(String text){
        if(StrUtil.isBlank(text)){
            return null;
        }
        String jsonStr = jsonParse(text);
        if(StrUtil.isNotBlank(jsonStr)){
            return jsonStr;
        }

        // 处理 \\r\\n
        text = text.replaceAll("\\\\r\\\\n", "");

        // 处理 \\n\\r
        text = text.replaceAll("\\\\n\\\\r", "");

        // 处理 \\n, \\r
        text = text.replaceAll("\\\\n", "").replaceAll("\\\\r", "")
                .replaceAll("\n", "")   .replaceAll("\r", "");

        // 处理 \t
        text = text.replaceAll("\\\\t", " ").replaceAll("\t", " ");

        if(DEBUG) log.info("repairResponse 修复: {}", text);
        return text;
    }

    private static String gsonString(String text){
        if(text.startsWith("{")){
            try {
                return GSON.toJson(GSON.fromJson(text, Dict.class));
            } catch (Exception e) {
                if(DEBUG) log.error("Gson Object 解析异常: {}", text);
                return null;
            }
        }
        else if(text.startsWith("[")){
            try {
                return GSON.toJson(GSON.fromJson(text, Collection.class));
            } catch (Exception e) {
                if(DEBUG) log.error("Gson Array 解析异常: ", e);
                return null;
            }
        }
        return null;
    }

    private static String jacksonString(String text){
        if(text.startsWith("{")){
            try {
                return toJacksonString(OBJECT_MAPPER.readValue(text
                        , OBJECT_MAPPER.getTypeFactory().constructType(Dict.class)));
            } catch (Exception e) {
                if(DEBUG) log.error("Jackson Object 解析异常: {}", text);
                return null;
            }
        }
        else if(text.startsWith("[")){
            try {
                return toJacksonString(OBJECT_MAPPER.readValue(text
                        , OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, Dict.class)));
            } catch (Exception e) {
                if(DEBUG) log.error("Jackson Array 解析异常: ", e);
                return null;
            }
        }
        return null;
    }

    private static String toJacksonString(Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            log.error("Jackson 字符串异常: ", e);
            return null;
        }
    }

}
