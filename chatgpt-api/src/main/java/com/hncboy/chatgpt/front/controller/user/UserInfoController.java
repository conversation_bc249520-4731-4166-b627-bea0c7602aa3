package com.hncboy.chatgpt.front.controller.user;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.domain.dto.UserBaseInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.UserConfigDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxPayOrderVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.enums.ApiKeyModelEnum;
import com.hncboy.chatgpt.front.framework.enums.MemberEnum;
import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.framework.exception.AuthException;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.WebUtil;
import com.hncboy.chatgpt.front.service.*;
import com.hncboy.chatgpt.tarot.helper.InviteTreeHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.WX_TOCLET_URL;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/10
 */
@FrontPreAuth
@AllArgsConstructor
@Tag(name = "用户相关接口")
@RestController
@RequestMapping("/user")
@Slf4j
public class UserInfoController {
    private final WxUserInfoService wxUserInfoService;
    private final UserBaseInfoService userBaseInfoService;
    private final WxPayService wxPayService;
    private final WxMpService wxService;
    private final WxPayOrderService wxPayOrderService;
    private final PrompterInfoService prompterInfoService;
    private final UserConfigService userConfigService;
    private final AppSignService appSignService;
    private final InviteTreeHelper inviteTreeHelper;


    @Operation(summary = "获取用户信息")
    @GetMapping("/info")
    public R<Object> getUserInfo() {
        WxUserInfoVO wxUserInfo = wxUserInfoService.queryUserInfoByOpenId(
                CurrentUserUtil.getUserId());
        return R.data(wxUserInfo);

    }

    @Operation(summary = "获取微信用户信息")
    @GetMapping("/wxInfo")
    public R<Object> getWxUserInfo() throws WxErrorException {
        wxService.switchover(WxAppIdEnum.zns.getCode());

        boolean a = true;
        int b = 0;
        while (a){
            WxUserInfo unionId = wxUserInfoService.getOne(new QueryWrapper<WxUserInfo>()
                    .isNull("union_id").last("limit 1"));
            if(b>1000){
                a=false;
            }
            if(unionId==null){
                break;
            }
            WxMpUser wxMpUser = wxService.getUserService().userInfo(unionId.getOpenId());
            unionId.setUnionId(wxMpUser.getUnionId());
            wxUserInfoService.updateById(unionId);

            b++;
        }




        return R.success();
    }

    @Operation(summary = "获取用户信息")
    @GetMapping("/v1/info")
    public R<UserBaseInfoVO> getUserInfoV1() {
        return R.data(userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId()));
    }

    @Operation(summary = "获取用户分佣信息")
    @GetMapping("/v1/info/commission")
    public R<InviteTreeHelper.TreeNode> getUserCommission() {
        return R.data(inviteTreeHelper.getInviteTree(CurrentUserUtil.getV2UserId()));
    }


    @Operation(summary = "生成支付订单")
    @GetMapping("/createOrder")
    public R<Object> createOrder(Integer goodsId) {
        try {
            MemberEnum memberEnum = MemberEnum.valueOfKey(goodsId);
            WxPayUnifiedOrderRequest request = new WxPayUnifiedOrderRequest();
            request.setBody("会员充值" + memberEnum.getTitle());
            request.setOpenid(CurrentUserUtil.getUserId());
            request.setSpbillCreateIp(WebUtil.getIp());
            request.setNotifyUrl("https://openai.alwzc.com/api/pay/notify/order");
            request.setTradeType("JSAPI");
            String orderNo = RandomUtil.randomString(32);
            request.setOutTradeNo(orderNo);
            request.setTotalFee(memberEnum.getPrice() * 100);
            log.info("拉起支付，订单信息:{}", request);
            WxPayMpOrderResult order = this.wxPayService.createOrder(request);
            WxPayOrderVO wxOrderPay = wxPayOrderService.createWxOrderPay(request, goodsId);
            wxOrderPay.setWxPayMpOrderResult(order);
            return R.data(wxOrderPay);
        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "生成提词器")
    @GetMapping("/industry")
    public R<Object> saveIndustry(String industry) {
        if (StringUtils.isEmpty(industry)) {
            return R.fail("参数不能为空");
        }
        prompterInfoService.saveIndustry(industry);
        return R.success("成功");
    }


    @Operation(summary = "增加可用次数")
    @PostMapping("/addUserNumMp")
    public R<Object> chatProcess(@RequestBody JSONObject jsonObject) {
        Integer species = 0;
        //看完广告
        if (jsonObject.getBool("isEnded")) {
            //按照次数算
            Integer count = jsonObject.getInt("count");
            species = count * 2;
        }
        if (species > 100) {
            return R.fail("涉嫌作弊，已通知管理员！");
        }
        if (species > 0) {
            wxUserInfoService.addUserNumMp(CurrentUserUtil.getUserId(), species);
            jsonObject.set("species", species);
            jsonObject.set("date", DateUtil.now());
            UserConfigDTO userConfigDTO = new UserConfigDTO();
            userConfigDTO.setOpenid(CurrentUserUtil.getUserId());
            userConfigDTO.setContent(jsonObject.toString());
            userConfigDTO.setType(2);
            userConfigService.saveUserConfig(userConfigDTO);
        }
        return R.success("奖励已发放成功,获得" + species + "金币");
    }


    @GetMapping("/sign")
    @Operation(summary = "签到")
    public R signByUser() {
        boolean res = appSignService.signByUser(CurrentUserUtil.getUserId());
        return R.data(res);
    }

    @GetMapping("/querySignByUser")
    @Operation(summary = "计算连续签到天数")
    public R querySignByUser() {
        return R.data(appSignService.querySignByUser(CurrentUserUtil.getUserId()));
    }


    @GetMapping("/querySignMonthByUser")
    @Operation(summary = "获取签到详情")
    public R querySignMonthByUser(Integer type) {
        return R.data(appSignService.querySignMonthByUser(CurrentUserUtil.getUserId(), type));
    }


    @Operation(summary = "绑定父ID")
    @PostMapping("/bandDingParent")
    public void bandDingParent(@RequestBody WxUserInfo wxUserInfo) {
        if (StrUtil.isNotEmpty(wxUserInfo.getParentId())) {
            wxUserInfo.setParentId(wxUserInfo.getParentId().replaceAll("#/", ""));
        }
        if (StrUtil.equals(wxUserInfo.getOpenId(), wxUserInfo.getParentId())) {
            return;
        }
        wxUserInfoService.bandDingParent(wxUserInfo.getOpenId(), wxUserInfo.getParentId());
    }


    @GetMapping("/querySonUserList")
    @Operation(summary = "查询下级")
    public R querySonUserList() {
        return R.data(wxUserInfoService.querySonUserList());
    }


    @Operation(summary = "保存用户配置")
    @GetMapping("/saveUserConfig")
    public void saveUserConfig(String type) {
        if (StrUtil.isEmpty(type)) {
            return;
        }
        if ("2".equals(type)) {
            boolean b = wxUserInfoService.checkUserIsVip(CurrentUserUtil.getUserId());
            if (!b) {
                throw new ServiceException("您还不是会员，无法使用该功能");
            }
        }
        UserConfigDTO userConfigDTO = new UserConfigDTO();
        Map map = new HashMap<>();
        map.put("model", type.equals("1") ? ApiKeyModelEnum.GPT_3_5_TURBO_16k_0613.getName() : ApiKeyModelEnum.GPT_4.getName());
        userConfigDTO.setType(3);
        userConfigDTO.setContent(JSONUtil.toJsonStr(map).toString());
        userConfigDTO.setOpenid(CurrentUserUtil.getUserId());
        userConfigService.saveOrUpdate(userConfigDTO);
    }

    @Operation(summary = "更新用户信息")
    @PostMapping("/v2/update")
    public R<String> update(@RequestBody UserBaseInfoDTO userInfoDTO) {
        userBaseInfoService.updateUserInfoById(userInfoDTO);
        return R.data("success");
    }


    @Operation(summary = "绑定微信")
    @GetMapping("/bindWx")
    public R<Object> createQrCode(HttpServletRequest httpRequest) {
        try {
            UserBaseInfo userBaseInfoByUserId = userBaseInfoService.getUserBaseInfoByUserId(CurrentUserUtil.getV2UserId());
            if (userBaseInfoByUserId == null) {
                throw new AuthException("请登录后再试");
            }
            if (StrUtil.isNotEmpty(userBaseInfoByUserId.getOpenId())) {
                return R.fail("您已经绑定过微信，请勿重复绑定");
            }
            // 获取二维码ticket 60秒过期
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxMpQrCodeTicket wxMpQrCodeTicket = wxService.getQrcodeService().qrCodeCreateTmpTicket("bind" + CurrentUserUtil.getV2UserId(), 60);
            // 链接后面拼上 Ticket 换取二维码图片
            wxMpQrCodeTicket.setUrl(WX_TOCLET_URL + wxMpQrCodeTicket.getTicket());
            return R.data(wxMpQrCodeTicket);
        } catch (WxErrorException e) {
            log.error("二维码获取失败", e);
            return R.fail("二维码获取失败");
        }
    }


    @Operation(summary = "更新用户信息")
    @PostMapping("/v2/updateStaus")
    public R<String> updateStatus() {
        userBaseInfoService.updateUserFirstStatusById(CurrentUserUtil.getV2UserId());
        return R.data("success");
    }

    @Operation(summary = "塔罗注销用户信息（软删除）")
    @DeleteMapping("/cancel")
    public R<String> cancel() {
        Integer id = CurrentUserUtil.getCurrentUser().getId();
        UserBaseInfoVO userBaseInfoVO = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());

        String userType = userBaseInfoVO.getUserType();
        String account = userBaseInfoVO.getAccount();
        if(userType==null||account==null||!userType.equals("tarot")||!account.substring(0,3).equals("wx_")){
            throw new ServiceException("非塔罗用户，不可注销");
        }
        userBaseInfoService.updateUserInfoIsDeleteById(id);
        log.info("id为{}的用户注销了账户",id);
        return R.data("success");
    }



}
