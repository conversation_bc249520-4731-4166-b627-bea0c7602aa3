spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: super_gpt
    password: F4etrzBRayCNKePd
    url: ${JDBC_URL:*****************************************************************************************************************************************************************************************************************************************}
  redis:
    host: 127.0.0.1
    password: redispassword
    port: 5388
    timeout: 20000
    lettuce:
      pool:
        max-active: 30
        max-idle: 7
        min-idle: 2
        max-wait: 20000
    database: 2

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mj:
  discord:
    guild-id: 1133302321749700670
    channel-id: 1133302322483707917
    user-token: ODI2NDA0MDI1MzU2MjU1Mjkz.GD3MqO.-SL1-8a7uZqyoYWQGfVsU4mWwWexPLXOBdURog
    user-wss: true
    user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
    bot-token: MTEzMzMwMzI3NzY0ODM2MzU2MQ.GZO0tl.-UVvhFjNjcMxEwu14m_qJ24jzklXSJ1Gx7X9Vc
  task-store:
    type: redis
    timeout: 1d
  translate-way: null
  id-prefix: "["
  id-suffix: "]"
  queue:
    timeout-minutes: 35
    core-size: 8
    queue-size: 3
  openai:
    gpt-api-key: ***************************************************
  ngDiscord:
    cdn: http://image.alwzc.com/discord

chat:
  # HTTP 代理
  http_proxy_host:
  # HTTP 代理
  http_proxy_port:

  # 默认配置
  openai_api_model: gpt-3.5-turbo-instruct
  openai_api_base_url: https://mjapi.cn/
  openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

  # 该通道的3.5模型免费
  gpt35_openai_api_base_url: https://29qg.com/
  gpt35_openai_api_key: sk-FRpBbKy7prilAA6U406a8e64D89746DcA387245fD95a9023

  # 该通道的4模型价格便宜
  gpt4_openai_api_base_url: https://29qg.com/
  gpt4_openai_api_key: sk-FRpBbKy7prilAA6U406a8e64D89746DcA387245fD95a9023

  # 该通道支持4-all模型，能处理附件
  gpt4all_openai_api_base_url: https://mjapi.cn/
  gpt4all_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

  # 该通道支持gpts模型
  gpts_openai_api_base_url: https://mjapi.cn/
  gpts_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO

dingtalk:
  secret: SECb68c6eac3b7330ddb5704147d6f16025d3db2a067092cebf109f322b1361cbd1
  webhook: https://oapi.dingtalk.com/robot/send?access_token=b5dae4c051d7b6fb72ddc78c3bc424f213dde811de83b3dafdf9e7c35cc6d192
  webhook_tarot_monitor: https://oapi.dingtalk.com/robot/send?access_token=440066d3d1bd2b92f0e5d2d6495e3ea5b072641d1678e2b1ccf4d93be1008b88
  webhook_tarot_exception: https://oapi.dingtalk.com/robot/send?access_token=c73824324412db4bdcdf7af4f4e9cbed84ab32911ff553dd24c4c516768b98a9
  schedule_enabled: true

wx:
  pay:
    configs:
      - appId: wx734d3d8a6888e18f # 第一个公众号的appid
        mchId: 1683747767 #微信支付商户号
        mchKey: MDZBRDM5NzU0OTg0NkMwMUMzRThFQkQy #微信支付商户密钥
        subAppId: #服务商模式下的子商户公众账号ID
        subMchId: #服务商模式下的子商户号
        keyPath: /www/wwwroot/jar/zns/apiclient_cert.p12 #p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
        privateKeyPath: /www/wwwroot/jar/zns/apiclient_key.pem
        privateCertPath: /www/wwwroot/jar/zns/apiclient_cert.pem #
        notifyUrl: https://zns.zjfdsr.com/api/notify/order/zns # 支付回调地址
        apiV3Key: MDZBRDM5NzU0OTg0NkMwMUMzRThFQkQy #微信支付V3密钥
      - appId: wx734d3d8a6888e18f # 第一个公众号的appid
        mchId: 1717772719 #微信支付商户号
        mchKey: MDZBRDM5NzUFdsR0NkMwMUMzRThFQkQy #微信支付商户密钥
        subAppId: #服务商模式下的子商户公众账号ID
        subMchId: #服务商模式下的子商户号
        keyPath: /www/wwwroot/jar/tarot/apiclient_cert.p12 #p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
        privateKeyPath: /www/wwwroot/jar/tarot/apiclient_key.pem
        privateCertPath: /www/wwwroot/jar/tarot/apiclient_cert.pem #
        notifyUrl: https://zns.zjfdsr.com/api/notify/order/tarot # 支付回调地址
        apiV3Key: MDZBRDM5NzUFdsR0NkMwMUMzRThFQkQy #微信支付V3密钥
  miniapp:
    configs:
      - appid: wxfe365e97c4eb6f9f
        secret: e494427b392b2e1f5adfbe61e828dbd9
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
  mp:
    configs:
      - appId: wx734d3d8a6888e18f # 第一个公众号的appid
        secret: e54bde3b6a97b435cf43526f3f5e0fb5 # 公众号的appsecret
        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值
      - appId: wx8aa8b47c44e06a98        # 第一个公众号的appid
        secret: 73f75b816c313814d4230b75e855c07c # 公众号的appsecret
        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值
      - appId: wx94dd418a44321f70        # 第一个公众号的appid
        secret: cd81945f300e0d90b6a0446756878765 # 公众号的appsecret
        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值
      - appId: wx7360397aa6007725        # 第一个公众号的appid
        secret: 580db91ee4a37ab8688d1b8a8f352561 # 公众号的appsecret
        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值

chatOI:
  url: https://chatoi.zjfdsr.com/api/v1/
